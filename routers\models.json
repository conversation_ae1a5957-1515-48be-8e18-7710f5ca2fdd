{"data": [{"id": "claude-3.7-sonnet:thinking", "object": "model", "created": **********, "owned_by": "anthropic", "name": "Claude 3.7 Sonnet (thinking)", "openai": {"id": "claude-3.7-sonnet:thinking", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Claude 3.7 Sonnet is an advanced large language model with improved reasoning, coding, and problem-solving capabilities. It introduces a hybrid reasoning approach, allowing users to choose between rapid responses and extended, step-by-step processing for complex tasks. The model demonstrates notable improvements in coding, particularly in front-end development and full-stack updates, and excels in agentic workflows, where it can autonomously navigate multi-step processes.\n\nClaude 3.7 Sonnet maintains performance parity with its predecessor in standard mode while offering an extended reasoning mode for enhanced accuracy in math, coding, and instruction-following tasks.", "modalities": {"input": ["text", "image", "file"], "output": ["text"]}, "provider": "anthropic", "profile_image_url": "/static/anthropic.png"}}, "context_length": 200000, "recommended_model": true, "reasoning_level": "heavy", "auto_select_priority": 0, "premium_model": false, "supports_function_calling": true, "actions": [], "tags": []}, {"id": "claude-sonnet-4", "object": "model", "created": **********, "owned_by": "anthropic", "name": "<PERSON> 4", "openai": {"id": "claude-sonnet-4", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Claude Sonnet 4 significantly enhances the capabilities of its predecessor, Sonnet 3.7, excelling in both coding and reasoning tasks with improved precision and controllability. Achieving state-of-the-art performance on SWE-bench (72.7%), Sonnet 4 balances capability and computational efficiency, making it suitable for a broad range of applications from routine coding tasks to complex software development projects. Key enhancements include improved autonomous codebase navigation, reduced error rates in agent-driven workflows, and increased reliability in following intricate instructions. Sonnet 4 is optimized for practical everyday use, providing advanced reasoning capabilities while maintaining efficiency and responsiveness in diverse internal and external scenarios.\n\nRead more at the [blog post here](https://www.anthropic.com/news/claude-4)", "modalities": {"input": ["image", "text", "file"], "output": ["text"]}, "provider": "anthropic", "profile_image_url": "/static/anthropic.png"}}, "context_length": 200000, "recommended_model": true, "reasoning_level": "fast", "auto_select_priority": 0, "premium_model": false, "supports_function_calling": true, "actions": [], "tags": []}, {"id": "gpt-4o", "object": "model", "created": **********, "owned_by": "openai", "name": "GPT-4o", "openai": {"id": "gpt-4o", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "GPT-4o (\"o\" for \"omni\") is OpenAI's latest AI model, supporting both text and image inputs with text outputs. It maintains the intelligence level of [GPT-4 Turbo](/models/openai/gpt-4-turbo) while being twice as fast and 50% more cost-effective. GPT-4o also offers improved performance in processing non-English languages and enhanced visual capabilities.\n\nFor benchmarking against other models, it was briefly called [\"im-also-a-good-gpt2-chatbot\"](https://twitter.com/LiamFedus/status/1790064963966370209)\n\n#multimodal", "modalities": {"input": ["text", "image", "file"], "output": ["text"]}, "provider": "openai", "profile_image_url": "/static/openai.png"}}, "context_length": 128000, "recommended_model": true, "reasoning_level": "fast", "auto_select_priority": 0, "premium_model": false, "supports_function_calling": true, "actions": [], "tags": []}, {"id": "llama-4-maverick", "object": "model", "created": **********, "owned_by": "meta-llama", "name": "Llama 4 Maverick", "openai": {"id": "llama-4-maverick", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Llama 4 Maverick 17B Instruct (128E) is a high-capacity multimodal language model from Meta, built on a mixture-of-experts (MoE) architecture with 128 experts and 17 billion active parameters per forward pass (400B total). It supports multilingual text and image input, and produces multilingual text and code output across 12 supported languages. Optimized for vision-language tasks, Maverick is instruction-tuned for assistant-like behavior, image reasoning, and general-purpose multimodal interaction.\n\nMaverick features early fusion for native multimodality and a 1 million token context window. It was trained on a curated mixture of public, licensed, and Meta-platform data, covering ~22 trillion tokens, with a knowledge cutoff in August 2024. Released on April 5, 2025 under the Llama 4 Community License, Maverick is suited for research and commercial applications requiring advanced multimodal understanding and high model throughput.", "modalities": {"input": ["text", "image"], "output": ["text"]}, "provider": "meta-llama", "profile_image_url": "/static/meta.png"}}, "context_length": 131072, "recommended_model": true, "reasoning_level": "fast", "premium_model": false, "supports_function_calling": true, "actions": [], "tags": []}, {"id": "sonar-deep-research", "object": "model", "created": **********, "owned_by": "perplexity", "name": "Sonar Deep Research", "openai": {"id": "sonar-deep-research", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Sonar Deep Research is a research-focused model designed for multi-step retrieval, synthesis, and reasoning across complex topics. It autonomously searches, reads, and evaluates sources, refining its approach as it gathers information. This enables comprehensive report generation across domains like finance, technology, health, and current events.\n\nNotes on Pricing ([Source](https://docs.perplexity.ai/guides/pricing#detailed-pricing-breakdown-for-sonar-deep-research)) \n- Input tokens comprise of Prompt tokens (user prompt) + Citation tokens (these are processed tokens from running searches)\n- Deep Research runs multiple searches to conduct exhaustive research. Searches are priced at $5/1000 searches. A request that does 30 searches will cost $0.15 in this step.\n- Reasoning is a distinct step in Deep Research since it does extensive automated reasoning through all the material it gathers during its research phase. Reasoning tokens here are a bit different than the CoTs in the answer - these are tokens that we use to reason through the research material prior to generating the outputs via the CoTs. Reasoning tokens are priced at $3/1M tokens", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "perplexity", "profile_image_url": "/static/perplexity.png"}}, "context_length": 200000, "recommended_model": true, "reasoning_level": "heavy", "auto_select_priority": 1, "premium_model": true, "supports_function_calling": false, "actions": [], "tags": []}, {"id": "sonar-pro", "object": "model", "created": **********, "owned_by": "perplexity", "name": "Sonar Pro", "openai": {"id": "sonar-pro", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Note: Sonar Pro pricing includes Perplexity search pricing. See [details here](https://docs.perplexity.ai/guides/pricing#detailed-pricing-breakdown-for-sonar-reasoning-pro-and-sonar-pro)\n\nFor enterprises seeking more advanced capabilities, the Sonar Pro API can handle in-depth, multi-step queries with added extensibility, like double the number of citations per search as Sonar on average. Plus, with a larger context window, it can handle longer and more nuanced searches and follow-up questions. ", "modalities": {"input": ["text", "image"], "output": ["text"]}, "provider": "perplexity", "profile_image_url": "/static/perplexity.png"}}, "context_length": 200000, "recommended_model": true, "reasoning_level": "light", "auto_select_priority": 0, "premium_model": false, "supports_function_calling": false, "actions": [], "tags": []}, {"id": "aion-1.0", "object": "model", "created": **********, "owned_by": "aion-labs", "name": "Aion-1.0", "openai": {"id": "aion-1.0", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Aion-1.0 is a multi-model system designed for high performance across various tasks, including reasoning and coding. It is built on DeepSeek-R1, augmented with additional models and techniques such as Tree of Thoughts (ToT) and Mixture of Experts (MoE). It is Aion Lab's most powerful reasoning model.", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "aion-labs", "profile_image_url": "/static/favicon.png"}}, "context_length": 131072, "reasoning_level": "fast", "premium_model": false, "supports_function_calling": false, "actions": [], "tags": []}, {"id": "aion-1.0-mini", "object": "model", "created": **********, "owned_by": "aion-labs", "name": "Aion-1.0-Mini", "openai": {"id": "aion-1.0-mini", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Aion-1.0-Mini 32B parameter model is a distilled version of the DeepSeek-R1 model, designed for strong performance in reasoning domains such as mathematics, coding, and logic. It is a modified variant of a FuseAI model that outperforms R1-Distill-Qwen-32B and R1-Distill-Llama-70B, with benchmark results available on its [Hugging Face page](https://huggingface.co/FuseAI/FuseO1-DeepSeekR1-QwQ-SkyT1-32B-Preview), independently replicated for verification.", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "aion-labs", "profile_image_url": "/static/favicon.png"}}, "context_length": 131072, "reasoning_level": "fast", "premium_model": false, "supports_function_calling": false, "actions": [], "tags": []}, {"id": "claude-3.5-haiku", "object": "model", "created": **********, "owned_by": "anthropic", "name": "Claude 3.5 Haiku", "openai": {"id": "claude-3.5-haiku", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Claude 3.5 Haiku features offers enhanced capabilities in speed, coding accuracy, and tool use. Engineered to excel in real-time applications, it delivers quick response times that are essential for dynamic tasks such as chat interactions and immediate coding suggestions.\n\nThis makes it highly suitable for environments that demand both speed and precision, such as software development, customer service bots, and data management systems.\n\nThis model is currently pointing to [Claude 3.5 Haiku (2024-10-22)](/anthropic/claude-3-5-haiku-20241022).", "modalities": {"input": ["text", "image"], "output": ["text"]}, "provider": "anthropic", "profile_image_url": "/static/anthropic.png"}}, "context_length": 200000, "reasoning_level": "fast", "premium_model": false, "supports_function_calling": true, "actions": [], "tags": []}, {"id": "claude-3.5-sonnet", "object": "model", "created": **********, "owned_by": "anthropic", "name": "Claude 3.5 Sonnet", "openai": {"id": "claude-3.5-sonnet", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "New Claude 3.5 Sonnet delivers better-than-Opus capabilities, faster-than-Sonnet speeds, at the same Sonnet prices. Sonnet is particularly good at:\n\n- Coding: Scores ~49% on SWE-Bench Verified, higher than the last best score, and without any fancy prompt scaffolding\n- Data science: Augments human data science expertise; navigates unstructured data while using multiple tools for insights\n- Visual processing: excelling at interpreting charts, graphs, and images, accurately transcribing text to derive insights beyond just the text alone\n- Agentic tasks: exceptional tool use, making it great at agentic tasks (i.e. complex, multi-step problem solving tasks that require engaging with other systems)\n\n#multimodal", "modalities": {"input": ["text", "image", "file"], "output": ["text"]}, "provider": "anthropic", "profile_image_url": "/static/anthropic.png"}}, "context_length": 200000, "reasoning_level": "fast", "premium_model": false, "supports_function_calling": true, "actions": [], "tags": []}, {"id": "claude-3.7-sonnet", "object": "model", "created": **********, "owned_by": "anthropic", "name": "Claude 3.7 Sonnet", "openai": {"id": "claude-3.7-sonnet", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Claude 3.7 Sonnet is an advanced large language model with improved reasoning, coding, and problem-solving capabilities. It introduces a hybrid reasoning approach, allowing users to choose between rapid responses and extended, step-by-step processing for complex tasks. The model demonstrates notable improvements in coding, particularly in front-end development and full-stack updates, and excels in agentic workflows, where it can autonomously navigate multi-step processes. \n\nClaude 3.7 Sonnet maintains performance parity with its predecessor in standard mode while offering an extended reasoning mode for enhanced accuracy in math, coding, and instruction-following tasks.\n\nRead more at the [blog post here](https://www.anthropic.com/news/claude-3-7-sonnet)", "modalities": {"input": ["text", "image", "file"], "output": ["text"]}, "provider": "anthropic", "profile_image_url": "/static/anthropic.png"}}, "context_length": 200000, "reasoning_level": "heavy", "premium_model": false, "supports_function_calling": true, "actions": [], "tags": []}, {"id": "claude-opus-4", "object": "model", "created": **********, "owned_by": "anthropic", "name": "<PERSON> 4", "openai": {"id": "claude-opus-4", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Claude Opus 4 is benchmarked as the world’s best coding model, at time of release, bringing sustained performance on complex, long-running tasks and agent workflows. It sets new benchmarks in software engineering, achieving leading results on SWE-bench (72.5%) and Terminal-bench (43.2%). Opus 4 supports extended, agentic workflows, handling thousands of task steps continuously for hours without degradation. \n\nRead more at the [blog post here](https://www.anthropic.com/news/claude-4)", "modalities": {"input": ["image", "text", "file"], "output": ["text"]}, "provider": "anthropic", "profile_image_url": "/static/anthropic.png"}}, "context_length": 200000, "reasoning_level": "heavy", "premium_model": true, "supports_function_calling": true, "actions": [], "tags": []}, {"id": "claude-opus-4-1", "object": "model", "created": **********, "owned_by": "anthropic", "name": "<PERSON> 4.1", "openai": {"id": "claude-opus-4-1", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"modalities": {"input": ["text"], "output": ["text"]}, "provider": "anthropic", "profile_image_url": "/static/anthropic.png"}}, "reasoning_level": "light", "premium_model": false, "supports_function_calling": false, "actions": [], "tags": []}, {"id": "claude-opus-4.1", "object": "model", "created": **********, "owned_by": "anthropic", "name": "<PERSON> 4.1", "openai": {"id": "claude-opus-4.1", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Claude Opus 4.1 is an updated version of Anthropic’s flagship model, offering improved performance in coding, reasoning, and agentic tasks. It achieves 74.5% on SWE-bench Verified and shows notable gains in multi-file code refactoring, debugging precision, and detail-oriented reasoning. The model supports extended thinking up to 64K tokens and is optimized for tasks involving research, data analysis, and tool-assisted reasoning.", "modalities": {"input": ["image", "text", "file"], "output": ["text"]}, "provider": "anthropic", "profile_image_url": "/static/anthropic.png"}}, "context_length": 200000, "reasoning_level": "heavy", "premium_model": true, "supports_function_calling": true, "actions": [], "tags": []}, {"id": "coder-large", "object": "model", "created": **********, "owned_by": "arcee-ai", "name": "Coder Large", "openai": {"id": "coder-large", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Coder‑Large is a 32 B‑parameter offspring of Qwen 2.5‑Instruct that has been further trained on permissively‑licensed GitHub, CodeSearchNet and synthetic bug‑fix corpora. It supports a 32k context window, enabling multi‑file refactoring or long diff review in a single call, and understands 30‑plus programming languages with special attention to TypeScript, Go and Terraform. Internal benchmarks show 5–8 pt gains over CodeLlama‑34 B‑Python on HumanEval and competitive BugFix scores thanks to a reinforcement pass that rewards compilable output. The model emits structured explanations alongside code blocks by default, making it suitable for educational tooling as well as production copilot scenarios. Cost‑wise, Together AI prices it well below proprietary incumbents, so teams can scale interactive coding without runaway spend. ", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "arcee-ai", "profile_image_url": "/static/favicon.png"}}, "context_length": 32768, "reasoning_level": "fast", "premium_model": false, "supports_function_calling": false, "actions": [], "tags": []}, {"id": "codestral-2501", "object": "model", "created": **********, "owned_by": "mist<PERSON><PERSON>", "name": "Codestral 2501", "openai": {"id": "codestral-2501", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "[Mistral](/mistralai)'s cutting-edge language model for coding. Codestral specializes in low-latency, high-frequency tasks such as fill-in-the-middle (FIM), code correction and test generation. \n\nLearn more on their blog post: https://mistral.ai/news/codestral-2501/", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "mist<PERSON><PERSON>", "profile_image_url": "/static/favicon.png"}}, "context_length": 262144, "reasoning_level": "fast", "premium_model": false, "supports_function_calling": true, "actions": [], "tags": []}, {"id": "codestral-2508", "object": "model", "created": **********, "owned_by": "mist<PERSON><PERSON>", "name": "Codestral 2508", "openai": {"id": "codestral-2508", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Mistral's cutting-edge language model for coding released end of July 2025. Codestral specializes in low-latency, high-frequency tasks such as fill-in-the-middle (FIM), code correction and test generation.\n\n[Blog Post](https://mistral.ai/news/codestral-25-08)", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "mist<PERSON><PERSON>", "profile_image_url": "/static/favicon.png"}}, "context_length": 256000, "reasoning_level": "light", "premium_model": false, "supports_function_calling": true, "actions": [], "tags": []}, {"id": "command", "object": "model", "created": **********, "owned_by": "cohere", "name": "Command", "openai": {"id": "command", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Command is an instruction-following conversational model that performs language tasks with high quality, more reliably and with a longer context than our base generative models.\n\nUse of this model is subject to Cohere's [Usage Policy](https://docs.cohere.com/docs/usage-policy) and [SaaS Agreement](https://cohere.com/saas-agreement).", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "cohere", "profile_image_url": "/static/cohere.png"}}, "context_length": 4096, "reasoning_level": "fast", "premium_model": false, "supports_function_calling": false, "actions": [], "tags": []}, {"id": "command-a", "object": "model", "created": **********, "owned_by": "cohere", "name": "Command A", "openai": {"id": "command-a", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Command A is an open-weights 111B parameter model with a 256k context window focused on delivering great performance across agentic, multilingual, and coding use cases.\nCompared to other leading proprietary and open-weights models Command A delivers maximum performance with minimum hardware costs, excelling on business-critical agentic and multilingual tasks.", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "cohere", "profile_image_url": "/static/cohere.png"}}, "context_length": 256000, "reasoning_level": "fast", "premium_model": false, "supports_function_calling": false, "actions": [], "tags": []}, {"id": "command-r", "object": "model", "created": **********, "owned_by": "cohere", "name": "Command R", "openai": {"id": "command-r", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Command-R is a 35B parameter model that performs conversational language tasks at a higher quality, more reliably, and with a longer context than previous models. It can be used for complex workflows like code generation, retrieval augmented generation (RAG), tool use, and agents.\n\nRead the launch post [here](https://txt.cohere.com/command-r/).\n\nUse of this model is subject to Cohere's [Usage Policy](https://docs.cohere.com/docs/usage-policy) and [SaaS Agreement](https://cohere.com/saas-agreement).", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "cohere", "profile_image_url": "/static/cohere.png"}}, "context_length": 128000, "reasoning_level": "fast", "premium_model": false, "supports_function_calling": true, "actions": [], "tags": []}, {"id": "command-r-plus", "object": "model", "created": **********, "owned_by": "cohere", "name": "Command R+", "openai": {"id": "command-r-plus", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Command R+ is a new, 104B-parameter LLM from Cohere. It's useful for roleplay, general consumer usecases, and Retrieval Augmented Generation (RAG).\n\nIt offers multilingual support for ten key languages to facilitate global business operations. See benchmarks and the launch post [here](https://txt.cohere.com/command-r-plus-microsoft-azure/).\n\nUse of this model is subject to Cohere's [Usage Policy](https://docs.cohere.com/docs/usage-policy) and [SaaS Agreement](https://cohere.com/saas-agreement).", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "cohere", "profile_image_url": "/static/cohere.png"}}, "context_length": 128000, "reasoning_level": "fast", "premium_model": false, "supports_function_calling": true, "actions": [], "tags": []}, {"id": "deepseek-prover-v2", "object": "model", "created": **********, "owned_by": "deepseek", "name": "DeepSeek Prover V2", "openai": {"id": "deepseek-prover-v2", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "DeepSeek Prover V2 is a 671B parameter model, speculated to be geared towards logic and mathematics. Likely an upgrade from [DeepSeek-Prover-V1.5](https://huggingface.co/deepseek-ai/DeepSeek-Prover-V1.5-RL) Not much is known about the model yet, as DeepSeek released it on Hugging Face without an announcement or description.", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "deepseek", "profile_image_url": "/static/deepseek.png"}}, "context_length": 160000, "reasoning_level": "fast", "premium_model": false, "supports_function_calling": false, "actions": [], "tags": []}, {"id": "deepseek-r1t-chimera", "object": "model", "created": **********, "owned_by": "tngtech", "name": "DeepSeek R1T Chimera", "openai": {"id": "deepseek-r1t-chimera", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "DeepSeek-R1T-Chimera is created by merging DeepSeek-R1 and DeepSeek-V3 (0324), combining the reasoning capabilities of R1 with the token efficiency improvements of V3. It is based on a DeepSeek-MoE Transformer architecture and is optimized for general text generation tasks.\n\nThe model merges pretrained weights from both source models to balance performance across reasoning, efficiency, and instruction-following tasks. It is released under the MIT license and intended for research and commercial use.", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "tngtech", "profile_image_url": "/static/favicon.png"}}, "context_length": 163840, "reasoning_level": "light", "premium_model": false, "supports_function_calling": false, "actions": [], "tags": []}, {"id": "deepseek-chat", "object": "model", "created": **********, "owned_by": "deepseek", "name": "DeepSeek V3", "openai": {"id": "deepseek-chat", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "DeepSeek-V3 is the latest model from the DeepSeek team, building upon the instruction following and coding abilities of the previous versions. Pre-trained on nearly 15 trillion tokens, the reported evaluations reveal that the model outperforms other open-source models and rivals leading closed-source models.\n\nFor model details, please visit [the DeepSeek-V3 repo](https://github.com/deepseek-ai/DeepSeek-V3) for more information, or see the [launch announcement](https://api-docs.deepseek.com/news/news1226).", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "deepseek", "profile_image_url": "/static/deepseek.png"}}, "context_length": 163840, "reasoning_level": "light", "premium_model": false, "supports_function_calling": true, "actions": [], "tags": []}, {"id": "deepseek-v3-base", "object": "model", "created": **********, "owned_by": "deepseek", "name": "DeepSeek V3 Base", "openai": {"id": "deepseek-v3-base", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Note that this is a base model mostly meant for testing, you need to provide detailed prompts for the model to return useful responses. \n\nDeepSeek-V3 Base is a 671B parameter open Mixture-of-Experts (MoE) language model with 37B active parameters per forward pass and a context length of 128K tokens. Trained on 14.8T tokens using FP8 mixed precision, it achieves high training efficiency and stability, with strong performance across language, reasoning, math, and coding tasks. \n\nDeepSeek-V3 Base is the pre-trained model behind [DeepSeek V3](/deepseek/deepseek-chat-v3)", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "deepseek", "profile_image_url": "/static/deepseek.png"}}, "context_length": 163840, "reasoning_level": "light", "premium_model": false, "supports_function_calling": false, "actions": [], "tags": []}, {"id": "deepseek-chat-v3.1", "object": "model", "created": **********, "owned_by": "deepseek", "name": "DeepSeek V3.1", "openai": {"id": "deepseek-chat-v3.1", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "DeepSeek-V3.1 is a large hybrid reasoning model (671B parameters, 37B active) that supports both thinking and non-thinking modes via prompt templates. It extends the DeepSeek-V3 base with a two-phase long-context training process, reaching up to 128K tokens, and uses FP8 microscaling for efficient inference.\n\nThe model improves tool use, code generation, and reasoning efficiency, achieving performance comparable to DeepSeek-R1 on difficult benchmarks while responding more quickly. It supports structured tool calling, code agents, and search agents, making it suitable for research, coding, and agentic workflows.\n\nIt succeeds the [DeepSeek V3-0324](/deepseek/deepseek-chat-v3-0324) model and performs well on a variety of tasks.", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "deepseek", "profile_image_url": "/static/deepseek.png"}}, "context_length": 163840, "reasoning_level": "light", "premium_model": false, "supports_function_calling": true, "actions": [], "tags": []}, {"id": "devstral-medium", "object": "model", "created": **********, "owned_by": "mist<PERSON><PERSON>", "name": "Devstral Medium", "openai": {"id": "devstral-medium", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Devstral Medium is a high-performance code generation and agentic reasoning model developed jointly by Mistral AI and All Hands AI. Positioned as a step up from Devstral Small, it achieves 61.6% on SWE-Bench Verified, placing it ahead of Gemini 2.5 Pro and GPT-4.1 in code-related tasks, at a fraction of the cost. It is designed for generalization across prompt styles and tool use in code agents and frameworks.\n\nDevstral Medium is available via API only (not open-weight), and supports enterprise deployment on private infrastructure, with optional fine-tuning capabilities.", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "mist<PERSON><PERSON>", "profile_image_url": "/static/favicon.png"}}, "context_length": 131072, "reasoning_level": "light", "premium_model": false, "supports_function_calling": true, "actions": [], "tags": []}, {"id": "devstral-small", "object": "model", "created": **********, "owned_by": "mist<PERSON><PERSON>", "name": "Devstral Small", "openai": {"id": "devstral-small", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Devstral-Small-2505 is a 24B parameter agentic LLM fine-tuned from Mistral-Small-3.1, jointly developed by Mistral AI and All Hands AI for advanced software engineering tasks. It is optimized for codebase exploration, multi-file editing, and integration into coding agents, achieving state-of-the-art results on SWE-Bench Verified (46.8%).\n\nDevstral supports a 128k context window and uses a custom Tekken tokenizer. It is text-only, with the vision encoder removed, and is suitable for local deployment on high-end consumer hardware (e.g., RTX 4090, 32GB RAM Macs). Devstral is best used in agentic workflows via the OpenHands scaffold and is compatible with inference frameworks like vLLM, Transformers, and Ollama. It is released under the Apache 2.0 license.", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "mist<PERSON><PERSON>", "profile_image_url": "/static/favicon.png"}}, "context_length": 131072, "reasoning_level": "light", "premium_model": false, "supports_function_calling": true, "actions": [], "tags": []}, {"id": "devstral-small-2505", "object": "model", "created": **********, "owned_by": "mist<PERSON><PERSON>", "name": "Devstral Small 2505", "openai": {"id": "devstral-small-2505", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Devstral-Small-2505 is a 24B parameter agentic LLM fine-tuned from Mistral-Small-3.1, jointly developed by Mistral AI and All Hands AI for advanced software engineering tasks. It is optimized for codebase exploration, multi-file editing, and integration into coding agents, achieving state-of-the-art results on SWE-Bench Verified (46.8%).\n\nDevstral supports a 128k context window and uses a custom Tekken tokenizer. It is text-only, with the vision encoder removed, and is suitable for local deployment on high-end consumer hardware (e.g., RTX 4090, 32GB RAM Macs). Devstral is best used in agentic workflows via the OpenHands scaffold and is compatible with inference frameworks like vLLM, Transformers, and Ollama. It is released under the Apache 2.0 license.", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "mist<PERSON><PERSON>", "profile_image_url": "/static/favicon.png"}}, "context_length": 128000, "reasoning_level": "light", "premium_model": false, "supports_function_calling": true, "actions": [], "tags": []}, {"id": "ernie-4.5-21b-a3b", "object": "model", "created": **********, "owned_by": "baidu", "name": "ERNIE 4.5 21B A3B", "openai": {"id": "ernie-4.5-21b-a3b", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "A sophisticated text-based Mixture-of-Experts (MoE) model featuring 21B total parameters with 3B activated per token, delivering exceptional multimodal understanding and generation through heterogeneous MoE structures and modality-isolated routing. Supporting an extensive 131K token context length, the model achieves efficient inference via multi-expert parallel collaboration and quantization, while advanced post-training techniques including SFT, DPO, and UPO ensure optimized performance across diverse applications with specialized routing and balancing losses for superior task handling.", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "baidu", "profile_image_url": "/static/favicon.png"}}, "context_length": 120000, "reasoning_level": "fast", "premium_model": false, "supports_function_calling": false, "actions": [], "tags": []}, {"id": "ernie-4.5-300b-a47b", "object": "model", "created": **********, "owned_by": "baidu", "name": "ERNIE 4.5 300B A47B ", "openai": {"id": "ernie-4.5-300b-a47b", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "ERNIE-4.5-300B-A47B is a 300B parameter Mixture-of-Experts (MoE) language model developed by <PERSON><PERSON> as part of the ERNIE 4.5 series. It activates 47B parameters per token and supports text generation in both English and Chinese. Optimized for high-throughput inference and efficient scaling, it uses a heterogeneous MoE structure with advanced routing and quantization strategies, including FP8 and 2-bit formats. This version is fine-tuned for language-only tasks and supports reasoning, tool parameters, and extended context lengths up to 131k tokens. Suitable for general-purpose LLM applications with high reasoning and throughput demands.", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "baidu", "profile_image_url": "/static/favicon.png"}}, "context_length": 123000, "reasoning_level": "fast", "premium_model": false, "supports_function_calling": false, "actions": [], "tags": []}, {"id": "ernie-4.5-vl-28b-a3b", "object": "model", "created": **********, "owned_by": "baidu", "name": "ERNIE 4.5 VL 28B A3B", "openai": {"id": "ernie-4.5-vl-28b-a3b", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "A powerful multimodal Mixture-of-Experts chat model featuring 28B total parameters with 3B activated per token, delivering exceptional text and vision understanding through its innovative heterogeneous MoE structure with modality-isolated routing. Built with scaling-efficient infrastructure for high-throughput training and inference, the model leverages advanced post-training techniques including SFT, DPO, and UPO for optimized performance, while supporting an impressive 131K context length and RLVR alignment for superior cross-modal reasoning and generation capabilities.", "modalities": {"input": ["text", "image"], "output": ["text"]}, "provider": "baidu", "profile_image_url": "/static/favicon.png"}}, "context_length": 30000, "reasoning_level": "fast", "premium_model": false, "supports_function_calling": false, "actions": [], "tags": []}, {"id": "ernie-4.5-vl-424b-a47b", "object": "model", "created": **********, "owned_by": "baidu", "name": "ERNIE 4.5 VL 424B A47B ", "openai": {"id": "ernie-4.5-vl-424b-a47b", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "ERNIE-4.5-VL-424B-A47B is a multimodal Mixture-of-Experts (MoE) model from Baidu’s ERNIE 4.5 series, featuring 424B total parameters with 47B active per token. It is trained jointly on text and image data using a heterogeneous MoE architecture and modality-isolated routing to enable high-fidelity cross-modal reasoning, image understanding, and long-context generation (up to 131k tokens). Fine-tuned with techniques like SFT, DPO, UPO, and RLVR, this model supports both “thinking” and non-thinking inference modes. Designed for vision-language tasks in English and Chinese, it is optimized for efficient scaling and can operate under 4-bit/8-bit quantization.", "modalities": {"input": ["image", "text"], "output": ["text"]}, "provider": "baidu", "profile_image_url": "/static/favicon.png"}}, "context_length": 123000, "reasoning_level": "fast", "premium_model": false, "supports_function_calling": false, "actions": [], "tags": []}, {"id": "glm-4-32b", "object": "model", "created": **********, "owned_by": "z-ai", "name": "GLM 4 32B ", "openai": {"id": "glm-4-32b", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "GLM 4 32B is a cost-effective foundation language model.\n\nIt can efficiently perform complex tasks and has significantly enhanced capabilities in tool use, online search, and code-related intelligent tasks.\n\nIt is made by the same lab behind the thudm models.", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "z-ai", "profile_image_url": "/static/favicon.png"}}, "context_length": 128000, "reasoning_level": "heavy", "premium_model": false, "supports_function_calling": true, "actions": [], "tags": []}, {"id": "glm-4.5", "object": "model", "created": **********, "owned_by": "z-ai", "name": "GLM 4.5", "openai": {"id": "glm-4.5", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "GLM-4.5 is our latest flagship foundation model, purpose-built for agent-based applications. It leverages a Mixture-of-Experts (MoE) architecture and supports a context length of up to 128k tokens. GLM-4.5 delivers significantly enhanced capabilities in reasoning, code generation, and agent alignment. It supports a hybrid inference mode with two options, a \"thinking mode\" designed for complex reasoning and tool use, and a \"non-thinking mode\" optimized for instant responses. Users can control the reasoning behaviour with the `reasoning` `enabled` boolean. [Learn more in our docs](https://openrouter.ai/docs/use-cases/reasoning-tokens#enable-reasoning-with-default-config)", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "z-ai", "profile_image_url": "/static/favicon.png"}}, "context_length": 131072, "reasoning_level": "heavy", "premium_model": false, "supports_function_calling": true, "actions": [], "tags": []}, {"id": "glm-4.5-air", "object": "model", "created": **********, "owned_by": "z-ai", "name": "GLM 4.5 Air", "openai": {"id": "glm-4.5-air", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "GLM-4.5-Air is the lightweight variant of our latest flagship model family, also purpose-built for agent-centric applications. Like GLM-4.5, it adopts the Mixture-of-Experts (MoE) architecture but with a more compact parameter size. GLM-4.5-Air also supports hybrid inference modes, offering a \"thinking mode\" for advanced reasoning and tool use, and a \"non-thinking mode\" for real-time interaction. Users can control the reasoning behaviour with the `reasoning` `enabled` boolean. [Learn more in our docs](https://openrouter.ai/docs/use-cases/reasoning-tokens#enable-reasoning-with-default-config)", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "z-ai", "profile_image_url": "/static/favicon.png"}}, "context_length": 128000, "reasoning_level": "light", "premium_model": false, "supports_function_calling": true, "actions": [], "tags": []}, {"id": "glm-4.5v", "object": "model", "created": **********, "owned_by": "z-ai", "name": "GLM 4.5V", "openai": {"id": "glm-4.5v", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "GLM-4.5V is a vision-language foundation model for multimodal agent applications. Built on a Mixture-of-Experts (MoE) architecture with 106B parameters and 12B activated parameters, it achieves state-of-the-art results in video understanding, image Q&A, OCR, and document parsing, with strong gains in front-end web coding, grounding, and spatial reasoning. It offers a hybrid inference mode: a \"thinking mode\" for deep reasoning and a \"non-thinking mode\" for fast responses. Reasoning behavior can be toggled via the `reasoning` `enabled` boolean. [Learn more in our docs](https://openrouter.ai/docs/use-cases/reasoning-tokens#enable-reasoning-with-default-config)", "modalities": {"input": ["text", "image"], "output": ["text"]}, "provider": "z-ai", "profile_image_url": "/static/favicon.png"}}, "context_length": 65536, "reasoning_level": "light", "premium_model": false, "supports_function_calling": true, "actions": [], "tags": []}, {"id": "gpt-oss-120b", "object": "model", "created": **********, "owned_by": "openai", "name": "GPT OSS 120B", "openai": {"id": "gpt-oss-120b", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "gpt-oss-120b is an open-weight, 117B-parameter Mixture-of-Experts (MoE) language model from OpenAI designed for high-reasoning, agentic, and general-purpose production use cases. It activates 5.1B parameters per forward pass and is optimized to run on a single H100 GPU with native MXFP4 quantization. The model supports configurable reasoning depth, full chain-of-thought access, and native tool use, including function calling, browsing, and structured output generation.", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "openai", "profile_image_url": "/static/openai.png"}}, "context_length": 131072, "reasoning_level": "light", "premium_model": false, "supports_function_calling": true, "actions": [], "tags": []}, {"id": "gpt-oss-20b", "object": "model", "created": **********, "owned_by": "openai", "name": "GPT OSS 20B", "openai": {"id": "gpt-oss-20b", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "gpt-oss-20b is an open-weight 21B parameter model released by OpenAI under the Apache 2.0 license. It uses a Mixture-of-Experts (MoE) architecture with 3.6B active parameters per forward pass, optimized for lower-latency inference and deployability on consumer or single-GPU hardware. The model is trained in OpenAI’s Harmony response format and supports reasoning level configuration, fine-tuning, and agentic capabilities including function calling, tool use, and structured outputs.", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "openai", "profile_image_url": "/static/openai.png"}}, "context_length": 131072, "reasoning_level": "light", "premium_model": false, "supports_function_calling": true, "actions": [], "tags": []}, {"id": "gpt-3.5-turbo", "object": "model", "created": **********, "owned_by": "openai", "name": "GPT-3.5 Turbo", "openai": {"id": "gpt-3.5-turbo", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "GPT-3.5 Turbo was OpenAI's fastest model. It can understand and generate natural language or code, and is optimized for chat and traditional completion tasks.\nTraining data up to Sep 2021.", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "openai", "profile_image_url": "/static/openai.png"}}, "context_length": 16385, "reasoning_level": "fast", "premium_model": false, "supports_function_calling": true, "actions": [], "tags": []}, {"id": "gpt-4", "object": "model", "created": **********, "owned_by": "openai", "name": "GPT-4", "openai": {"id": "gpt-4", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "OpenAI's flagship model, GPT-4 is a large-scale multimodal language model capable of solving difficult problems with greater accuracy than previous models due to its broader general knowledge and advanced reasoning capabilities. Training data: up to Sep 2021.", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "openai", "profile_image_url": "/static/openai.png"}}, "context_length": 8191, "reasoning_level": "fast", "premium_model": true, "supports_function_calling": true, "actions": [], "tags": []}, {"id": "gpt-4-turbo", "object": "model", "created": **********, "owned_by": "openai", "name": "GPT-4 Turbo", "openai": {"id": "gpt-4-turbo", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "The latest GPT-4 Turbo model with vision capabilities. Vision requests can now use JSON mode and function calling.\n\nTraining data: up to December 2023.", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "openai", "profile_image_url": "/static/openai.png"}}, "context_length": 128000, "reasoning_level": "fast", "premium_model": false, "supports_function_calling": true, "actions": [], "tags": []}, {"id": "gpt-4.1", "object": "model", "created": **********, "owned_by": "openai", "name": "GPT-4.1", "openai": {"id": "gpt-4.1", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "GPT-4.1 is a flagship large language model optimized for advanced instruction following, real-world software engineering, and long-context reasoning. It supports a 1 million token context window and outperforms GPT-4o and GPT-4.5 across coding (54.6% SWE-bench Verified), instruction compliance (87.4% IFEval), and multimodal understanding benchmarks. It is tuned for precise code diffs, agent reliability, and high recall in large document contexts, making it ideal for agents, IDE tooling, and enterprise knowledge retrieval.", "modalities": {"input": ["image", "text", "file"], "output": ["text"]}, "provider": "openai", "profile_image_url": "/static/openai.png"}}, "context_length": 1047576, "reasoning_level": "fast", "premium_model": false, "supports_function_calling": true, "actions": [], "tags": []}, {"id": "gpt-4.1-mini", "object": "model", "created": **********, "owned_by": "openai", "name": "GPT-4.1 Mini", "openai": {"id": "gpt-4.1-mini", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "GPT-4.1 Mini is a mid-sized model delivering performance competitive with GPT-4o at substantially lower latency and cost. It retains a 1 million token context window and scores 45.1% on hard instruction evals, 35.8% on MultiChallenge, and 84.1% on IFEval. Mini also shows strong coding ability (e.g., 31.6% on Aid<PERSON>’s polyglot diff benchmark) and vision understanding, making it suitable for interactive applications with tight performance constraints.", "modalities": {"input": ["image", "text", "file"], "output": ["text"]}, "provider": "openai", "profile_image_url": "/static/openai.png"}}, "context_length": 1047576, "reasoning_level": "fast", "premium_model": false, "supports_function_calling": true, "actions": [], "tags": []}, {"id": "gpt-4.1-nano", "object": "model", "created": **********, "owned_by": "openai", "name": "GPT-4.1 Nano", "openai": {"id": "gpt-4.1-nano", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "For tasks that demand low latency, GPT‑4.1 nano is the fastest and cheapest model in the GPT-4.1 series. It delivers exceptional performance at a small size with its 1 million token context window, and scores 80.1% on MMLU, 50.3% on GPQA, and 9.8% on Aider polyglot coding – even higher than GPT‑4o mini. It’s ideal for tasks like classification or autocompletion.", "modalities": {"input": ["image", "text", "file"], "output": ["text"]}, "provider": "openai", "profile_image_url": "/static/openai.png"}}, "context_length": 1047576, "reasoning_level": "fast", "premium_model": false, "supports_function_calling": true, "actions": [], "tags": []}, {"id": "gpt-4o:extended", "object": "model", "created": **********, "owned_by": "openai", "name": "GPT-4o (extended)", "openai": {"id": "gpt-4o:extended", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "GPT-4o (\"o\" for \"omni\") is OpenAI's latest AI model, supporting both text and image inputs with text outputs. It maintains the intelligence level of [GPT-4 Turbo](/models/openai/gpt-4-turbo) while being twice as fast and 50% more cost-effective. GPT-4o also offers improved performance in processing non-English languages and enhanced visual capabilities.\n\nFor benchmarking against other models, it was briefly called [\"im-also-a-good-gpt2-chatbot\"](https://twitter.com/LiamFedus/status/1790064963966370209)\n\n#multimodal", "modalities": {"input": ["text", "image", "file"], "output": ["text"]}, "provider": "openai", "profile_image_url": "/static/openai.png"}}, "context_length": 128000, "reasoning_level": "light", "premium_model": false, "supports_function_calling": true, "actions": [], "tags": []}, {"id": "gpt-5", "object": "model", "created": **********, "owned_by": "openai", "name": "GPT-5", "openai": {"id": "gpt-5", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "GPT-5 is OpenAI’s most advanced model, offering major improvements in reasoning, code quality, and user experience. It is optimized for complex tasks that require step-by-step reasoning, instruction following, and accuracy in high-stakes use cases. It supports test-time routing features and advanced prompt understanding, including user-specified intent like \"think hard about this.\" Improvements include reductions in hallucination, sycophancy, and better performance in coding, writing, and health-related tasks.\n\nNote that BYOK is required for this model. Set up here: https://openrouter.ai/settings/integrations", "modalities": {"input": ["text", "image", "file"], "output": ["text"]}, "provider": "openai", "profile_image_url": "/static/openai.png"}}, "context_length": 400000, "reasoning_level": "light", "premium_model": false, "supports_function_calling": true, "actions": [], "tags": []}, {"id": "gpt-5-chat", "object": "model", "created": **********, "owned_by": "openai", "name": "GPT-5 Chat", "openai": {"id": "gpt-5-chat", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "GPT-5 Chat is designed for advanced, natural, multimodal, and context-aware conversations for enterprise applications.", "modalities": {"input": ["file", "image", "text"], "output": ["text"]}, "provider": "openai", "profile_image_url": "/static/openai.png"}}, "context_length": 400000, "reasoning_level": "light", "premium_model": false, "supports_function_calling": true, "actions": [], "tags": []}, {"id": "gpt-5-mini", "object": "model", "created": **********, "owned_by": "openai", "name": "GPT-5 Mini", "openai": {"id": "gpt-5-mini", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "GPT-5 Mini is a compact version of GPT-5, designed to handle lighter-weight reasoning tasks. It provides the same instruction-following and safety-tuning benefits as GPT-5, but with reduced latency and cost. GPT-5 Mini is the successor to OpenAI's o4-mini model.", "modalities": {"input": ["text", "image", "file"], "output": ["text"]}, "provider": "openai", "profile_image_url": "/static/openai.png"}}, "context_length": 400000, "reasoning_level": "light", "premium_model": false, "supports_function_calling": true, "actions": [], "tags": []}, {"id": "gpt-5-nano", "object": "model", "created": **********, "owned_by": "openai", "name": "GPT-5 Nano", "openai": {"id": "gpt-5-nano", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "GPT-5-Nano is the smallest and fastest variant in the GPT-5 system, optimized for developer tools, rapid interactions, and ultra-low latency environments. While limited in reasoning depth compared to its larger counterparts, it retains key instruction-following and safety features. It is the successor to GPT-4.1-nano and offers a lightweight option for cost-sensitive or real-time applications.", "modalities": {"input": ["text", "image", "file"], "output": ["text"]}, "provider": "openai", "profile_image_url": "/static/openai.png"}}, "context_length": 400000, "reasoning_level": "fast", "premium_model": false, "supports_function_calling": true, "actions": [], "tags": []}, {"id": "gemini-2.5-flash", "object": "model", "created": **********, "owned_by": "google", "name": "Gemini 2.5 Flash", "openai": {"id": "gemini-2.5-flash", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Gemini 2.5 Flash is Google's state-of-the-art workhorse model, specifically designed for advanced reasoning, coding, mathematics, and scientific tasks. It includes built-in \"thinking\" capabilities, enabling it to provide responses with greater accuracy and nuanced context handling. \n\nAdditionally, Gemini 2.5 Flash is configurable through the \"max tokens for reasoning\" parameter, as described in the documentation (https://openrouter.ai/docs/use-cases/reasoning-tokens#max-tokens-for-reasoning).", "modalities": {"input": ["file", "image", "text", "audio"], "output": ["text"]}, "provider": "google", "profile_image_url": "/static/google.png"}}, "context_length": 1048576, "reasoning_level": "light", "premium_model": false, "supports_function_calling": true, "actions": [], "tags": []}, {"id": "gemini-2.5-flash-lite", "object": "model", "created": **********, "owned_by": "google", "name": "Gemini 2.5 Flash Lite Preview 06-17", "openai": {"id": "gemini-2.5-flash-lite", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Gemini 2.5 Flash-Lite is a lightweight reasoning model in the Gemini 2.5 family, optimized for ultra-low latency and cost efficiency. It offers improved throughput, faster token generation, and better performance across common benchmarks compared to earlier Flash models. By default, \"thinking\" (i.e. multi-pass reasoning) is disabled to prioritize speed, but developers can enable it via the [Reasoning API parameter](https://openrouter.ai/docs/use-cases/reasoning-tokens) to selectively trade off cost for intelligence. ", "modalities": {"input": ["file", "image", "text", "audio"], "output": ["text"]}, "provider": "google", "profile_image_url": "/static/google.png"}}, "context_length": 1048576, "reasoning_level": "fast", "premium_model": false, "supports_function_calling": true, "actions": [], "tags": []}, {"id": "gemini-2.5-pro", "object": "model", "created": **********, "owned_by": "google", "name": "Gemini 2.5 Pro", "openai": {"id": "gemini-2.5-pro", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Gemini 2.5 Pro is Google’s state-of-the-art AI model designed for advanced reasoning, coding, mathematics, and scientific tasks. It employs “thinking” capabilities, enabling it to reason through responses with enhanced accuracy and nuanced context handling. Gemini 2.5 Pro achieves top-tier performance on multiple benchmarks, including first-place positioning on the LMArena leaderboard, reflecting superior human-preference alignment and complex problem-solving abilities.", "modalities": {"input": ["file", "image", "text", "audio"], "output": ["text"]}, "provider": "google", "profile_image_url": "/static/google.png"}}, "context_length": 1048576, "reasoning_level": "light", "premium_model": true, "supports_function_calling": true, "actions": [], "tags": []}, {"id": "gemma-3-12b-it", "object": "model", "created": **********, "owned_by": "google", "name": "Gemma 3 12B", "openai": {"id": "gemma-3-12b-it", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Gemma 3 introduces multimodality, supporting vision-language input and text outputs. It handles context windows up to 128k tokens, understands over 140 languages, and offers improved math, reasoning, and chat capabilities, including structured outputs and function calling. Gemma 3 12B is the second largest in the family of Gemma 3 models after [Gemma 3 27B](google/gemma-3-27b-it)", "modalities": {"input": ["text", "image"], "output": ["text"]}, "provider": "google", "profile_image_url": "/static/google.png"}}, "context_length": 131072, "reasoning_level": "fast", "premium_model": false, "supports_function_calling": false, "actions": [], "tags": []}, {"id": "gemma-3-27b-it", "object": "model", "created": **********, "owned_by": "google", "name": "Gemma 3 27B", "openai": {"id": "gemma-3-27b-it", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Gemma 3 introduces multimodality, supporting vision-language input and text outputs. It handles context windows up to 128k tokens, understands over 140 languages, and offers improved math, reasoning, and chat capabilities, including structured outputs and function calling. Gemma 3 27B is Google's latest open source model, successor to [Gemma 2](google/gemma-2-27b-it)", "modalities": {"input": ["text", "image"], "output": ["text"]}, "provider": "google", "profile_image_url": "/static/google.png"}}, "context_length": 131072, "reasoning_level": "fast", "premium_model": false, "supports_function_calling": false, "actions": [], "tags": []}, {"id": "gemma-3-4b-it", "object": "model", "created": **********, "owned_by": "google", "name": "Gemma 3 4B", "openai": {"id": "gemma-3-4b-it", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Gemma 3 introduces multimodality, supporting vision-language input and text outputs. It handles context windows up to 128k tokens, understands over 140 languages, and offers improved math, reasoning, and chat capabilities, including structured outputs and function calling.", "modalities": {"input": ["text", "image"], "output": ["text"]}, "provider": "google", "profile_image_url": "/static/google.png"}}, "context_length": 131072, "reasoning_level": "fast", "premium_model": false, "supports_function_calling": false, "actions": [], "tags": []}, {"id": "grok-2-1212", "object": "model", "created": **********, "owned_by": "x-ai", "name": "Grok 2 1212", "openai": {"id": "grok-2-1212", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Grok 2 1212 introduces significant enhancements to accuracy, instruction adherence, and multilingual support, making it a powerful and flexible choice for developers seeking a highly steerable, intelligent model.", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "x-ai", "profile_image_url": "/static/favicon.png"}}, "context_length": 131072, "reasoning_level": "light", "premium_model": false, "supports_function_calling": true, "actions": [], "tags": []}, {"id": "grok-3", "object": "model", "created": **********, "owned_by": "x-ai", "name": "Grok 3", "openai": {"id": "grok-3", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Grok 3 is the latest model from xAI. It's their flagship model that excels at enterprise use cases like data extraction, coding, and text summarization. Possesses deep domain knowledge in finance, healthcare, law, and science.", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "x-ai", "profile_image_url": "/static/favicon.png"}}, "context_length": 131072, "reasoning_level": "fast", "premium_model": false, "supports_function_calling": true, "actions": [], "tags": []}, {"id": "grok-3-mini", "object": "model", "created": **********, "owned_by": "x-ai", "name": "Grok 3 Mini", "openai": {"id": "grok-3-mini", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "A lightweight model that thinks before responding. Fast, smart, and great for logic-based tasks that do not require deep domain knowledge. The raw thinking traces are accessible.", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "x-ai", "profile_image_url": "/static/favicon.png"}}, "context_length": 131072, "reasoning_level": "fast", "premium_model": false, "supports_function_calling": true, "actions": [], "tags": []}, {"id": "grok-4", "object": "model", "created": **********, "owned_by": "x-ai", "name": "Grok 4", "openai": {"id": "grok-4", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Grok 4 is xAI's latest reasoning model with a 256k context window. It supports parallel tool calling, structured outputs, and both image and text inputs. Note that reasoning is not exposed, reasoning cannot be disabled, and the reasoning effort cannot be specified.", "modalities": {"input": ["image", "text"], "output": ["text"]}, "provider": "x-ai", "profile_image_url": "/static/favicon.png"}}, "context_length": 256000, "reasoning_level": "light", "premium_model": false, "supports_function_calling": true, "actions": [], "tags": []}, {"id": "hunyuan-a13b-instruct", "object": "model", "created": **********, "owned_by": "tencent", "name": "Hunyuan A13B Instruct", "openai": {"id": "hunyuan-a13b-instruct", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Hunyuan-A13B is a 13B active parameter Mixture-of-Experts (MoE) language model developed by Tencent, with a total parameter count of 80B and support for reasoning via Chain-of-Thought. It offers competitive benchmark performance across mathematics, science, coding, and multi-turn reasoning tasks, while maintaining high inference efficiency via Grouped Query Attention (GQA) and quantization support (FP8, GPTQ, etc.).", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "tencent", "profile_image_url": "/static/favicon.png"}}, "context_length": 32768, "reasoning_level": "light", "premium_model": false, "supports_function_calling": false, "actions": [], "tags": []}, {"id": "inflection-3-pi", "object": "model", "created": **********, "owned_by": "inflection", "name": "Inflection 3 Pi", "openai": {"id": "inflection-3-pi", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Inflection 3 Pi powers Inflection's [<PERSON>](https://pi.ai) chatbot, including backstory, emotional intelligence, productivity, and safety. It has access to recent news, and excels in scenarios like customer support and roleplay.\n\n<PERSON> has been trained to mirror your tone and style, if you use more emojis, so will Pi! Try experimenting with various prompts and conversation styles.", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "inflection", "profile_image_url": "/static/favicon.png"}}, "context_length": 8000, "reasoning_level": "fast", "premium_model": false, "supports_function_calling": false, "actions": [], "tags": []}, {"id": "inflection-3-productivity", "object": "model", "created": **********, "owned_by": "inflection", "name": "Inflection 3 Productivity", "openai": {"id": "inflection-3-productivity", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Inflection 3 Productivity is optimized for following instructions. It is better for tasks requiring JSON output or precise adherence to provided guidelines. It has access to recent news.\n\nFor emotional intelligence similar to Pi, see [Inflect 3 Pi](/inflection/inflection-3-pi)\n\nSee [Inflection's announcement](https://inflection.ai/blog/enterprise) for more details.", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "inflection", "profile_image_url": "/static/favicon.png"}}, "context_length": 8000, "reasoning_level": "fast", "premium_model": false, "supports_function_calling": false, "actions": [], "tags": []}, {"id": "kimi-k2", "object": "model", "created": **********, "owned_by": "moonshotai", "name": "Kimi K2", "openai": {"id": "kimi-k2", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Kimi K2 Instruct is a large-scale Mixture-of-Experts (MoE) language model developed by Moonshot AI, featuring 1 trillion total parameters with 32 billion active per forward pass. It is optimized for agentic capabilities, including advanced tool use, reasoning, and code synthesis. Kimi K2 excels across a broad range of benchmarks, particularly in coding (LiveCodeBench, SWE-bench), reasoning (ZebraLogic, GPQA), and tool-use (Tau2, AceBench) tasks. It supports long-context inference up to 128K tokens and is designed with a novel training stack that includes the MuonClip optimizer for stable large-scale MoE training.", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "moonshotai", "profile_image_url": "/static/favicon.png"}}, "context_length": 63000, "reasoning_level": "light", "premium_model": false, "supports_function_calling": true, "actions": [], "tags": []}, {"id": "kimi-vl-a3b-thinking", "object": "model", "created": **********, "owned_by": "moonshotai", "name": "Kimi VL A3B Thinking", "openai": {"id": "kimi-vl-a3b-thinking", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Kimi-VL is a lightweight Mixture-of-Experts vision-language model that activates only 2.8B parameters per step while delivering strong performance on multimodal reasoning and long-context tasks. The Kimi-VL-A3B-Thinking variant, fine-tuned with chain-of-thought and reinforcement learning, excels in math and visual reasoning benchmarks like MathVision, MMMU, and MathVista, rivaling much larger models such as Qwen2.5-VL-7B and Gemma-3-12B. It supports 128K context and high-resolution input via its MoonViT encoder.", "modalities": {"input": ["image", "text"], "output": ["text"]}, "provider": "moonshotai", "profile_image_url": "/static/favicon.png"}}, "context_length": 131072, "reasoning_level": "light", "premium_model": false, "supports_function_calling": false, "actions": [], "tags": []}, {"id": "lfm-3b", "object": "model", "created": **********, "owned_by": "liquid", "name": "LFM 3B", "openai": {"id": "lfm-3b", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Liquid's LFM 3B delivers incredible performance for its size. It positions itself as first place among 3B parameter transformers, hybrids, and RNN models It is also on par with Phi-3.5-mini on multiple benchmarks, while being 18.4% smaller.\n\nLFM-3B is the ideal choice for mobile and other edge text-based applications.\n\nSee the [launch announcement](https://www.liquid.ai/liquid-foundation-models) for benchmarks and more info.", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "liquid", "profile_image_url": "/static/favicon.png"}}, "context_length": 32768, "reasoning_level": "fast", "premium_model": false, "supports_function_calling": false, "actions": [], "tags": []}, {"id": "lfm-7b", "object": "model", "created": **********, "owned_by": "liquid", "name": "LFM 7B", "openai": {"id": "lfm-7b", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "LFM-7B, a new best-in-class language model. LFM-7B is designed for exceptional chat capabilities, including languages like Arabic and Japanese. Powered by the Liquid Foundation Model (LFM) architecture, it exhibits unique features like low memory footprint and fast inference speed. \n\nLFM-7B is the world’s best-in-class multilingual language model in English, Arabic, and Japanese.\n\nSee the [launch announcement](https://www.liquid.ai/lfm-7b) for benchmarks and more info.", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "liquid", "profile_image_url": "/static/favicon.png"}}, "context_length": 32768, "reasoning_level": "fast", "premium_model": false, "supports_function_calling": false, "actions": [], "tags": []}, {"id": "llama-3.3-70b-instruct", "object": "model", "created": **********, "owned_by": "meta-llama", "name": "Llama 3.3 70B Instruct", "openai": {"id": "llama-3.3-70b-instruct", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "The Meta Llama 3.3 multilingual large language model (LLM) is a pretrained and instruction tuned generative model in 70B (text in/text out). The Llama 3.3 instruction tuned text only model is optimized for multilingual dialogue use cases and outperforms many of the available open source and closed chat models on common industry benchmarks.\n\nSupported languages: English, German, French, Italian, Portuguese, Hindi, Spanish, and Thai.\n\n[Model Card](https://github.com/meta-llama/llama-models/blob/main/models/llama3_3/MODEL_CARD.md)", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "meta-llama", "profile_image_url": "/static/meta.png"}}, "context_length": 131072, "reasoning_level": "fast", "premium_model": false, "supports_function_calling": true, "actions": [], "tags": []}, {"id": "llama-3.3-nemotron-super-49b-v1", "object": "model", "created": **********, "owned_by": "nvidia", "name": "Llama 3.3 Nemotron Super 49B v1", "openai": {"id": "llama-3.3-nemotron-super-49b-v1", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Llama-3.3-Nemotron-Super-49B-v1 is a large language model (LLM) optimized for advanced reasoning, conversational interactions, retrieval-augmented generation (RAG), and tool-calling tasks. Derived from Meta's Llama-3.3-70B-Instruct, it employs a Neural Architecture Search (NAS) approach, significantly enhancing efficiency and reducing memory requirements. This allows the model to support a context length of up to 128K tokens and fit efficiently on single high-performance GPUs, such as NVIDIA H200.\n\nNote: you must include `detailed thinking on` in the system prompt to enable reasoning. Please see [Usage Recommendations](https://huggingface.co/nvidia/Llama-3_1-Nemotron-Ultra-253B-v1#quick-start-and-usage-recommendations) for more.", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "nvidia", "profile_image_url": "/static/favicon.png"}}, "context_length": 131072, "reasoning_level": "fast", "premium_model": false, "supports_function_calling": false, "actions": [], "tags": []}, {"id": "llama-4-scout", "object": "model", "created": **********, "owned_by": "meta-llama", "name": "Llama 4 Scout", "openai": {"id": "llama-4-scout", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Llama 4 Scout 17B Instruct (16E) is a mixture-of-experts (MoE) language model developed by Meta, activating 17 billion parameters out of a total of 109B. It supports native multimodal input (text and image) and multilingual output (text and code) across 12 supported languages. Designed for assistant-style interaction and visual reasoning, <PERSON> uses 16 experts per forward pass and features a context length of 10 million tokens, with a training corpus of ~40 trillion tokens.\n\nBuilt for high efficiency and local or commercial deployment, Llama 4 Scout incorporates early fusion for seamless modality integration. It is instruction-tuned for use in multilingual chat, captioning, and image understanding tasks. Released under the Llama 4 Community License, it was last trained on data up to August 2024 and launched publicly on April 5, 2025.", "modalities": {"input": ["text", "image"], "output": ["text"]}, "provider": "meta-llama", "profile_image_url": "/static/meta.png"}}, "context_length": 172000, "reasoning_level": "fast", "premium_model": false, "supports_function_calling": true, "actions": [], "tags": []}, {"id": "llemma_7b", "object": "model", "created": **********, "owned_by": "eleutherai", "name": "Llemma 7b", "openai": {"id": "llemma_7b", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Llemma 7B is a language model for mathematics. It was initialized with Code Llama 7B weights, and trained on the Proof-Pile-2 for 200B tokens. Llemma models are particularly strong at chain-of-thought mathematical reasoning and using computational tools for mathematics, such as Python and formal theorem provers.", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "eleutherai", "profile_image_url": "/static/favicon.png"}}, "context_length": 4096, "reasoning_level": "fast", "premium_model": false, "supports_function_calling": false, "actions": [], "tags": []}, {"id": "mai-ds-r1", "object": "model", "created": **********, "owned_by": "microsoft", "name": "MAI DS R1", "openai": {"id": "mai-ds-r1", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "MAI-DS-R1 is a post-trained variant of DeepSeek-R1 developed by the Microsoft AI team to improve the model’s responsiveness on previously blocked topics while enhancing its safety profile. Built on top of DeepSeek-R1’s reasoning foundation, it integrates 110k examples from the Tulu-3 SFT dataset and 350k internally curated multilingual safety-alignment samples. The model retains strong reasoning, coding, and problem-solving capabilities, while unblocking a wide range of prompts previously restricted in R1.\n\nMAI-DS-R1 demonstrates improved performance on harm mitigation benchmarks and maintains competitive results across general reasoning tasks. It surpasses R1-1776 in satisfaction metrics for blocked queries and reduces leakage in harmful content categories. The model is based on a transformer MoE architecture and is suitable for general-purpose use cases, excluding high-stakes domains such as legal, medical, or autonomous systems.", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "microsoft", "profile_image_url": "/static/microsoft.png"}}, "context_length": 163840, "reasoning_level": "light", "premium_model": false, "supports_function_calling": false, "actions": [], "tags": []}, {"id": "maestro-reasoning", "object": "model", "created": **********, "owned_by": "arcee-ai", "name": "Maestro Reasoning", "openai": {"id": "maestro-reasoning", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Maestro Reasoning is Arcee's flagship analysis model: a 32 B‑parameter derivative of Qwen 2.5‑32 B tuned with DPO and chain‑of‑thought RL for step‑by‑step logic. Compared to the earlier 7 B preview, the production 32 B release widens the context window to 128 k tokens and doubles pass‑rate on MATH and GSM‑8K, while also lifting code completion accuracy. Its instruction style encourages structured \"thought → answer\" traces that can be parsed or hidden according to user preference. That transparency pairs well with audit‑focused industries like finance or healthcare where seeing the reasoning path matters. In Arcee Conductor, <PERSON><PERSON> is automatically selected for complex, multi‑constraint queries that smaller SLMs bounce. ", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "arcee-ai", "profile_image_url": "/static/favicon.png"}}, "context_length": 131072, "reasoning_level": "fast", "premium_model": false, "supports_function_calling": false, "actions": [], "tags": []}, {"id": "magistral-medium-2506", "object": "model", "created": **********, "owned_by": "mist<PERSON><PERSON>", "name": "Magistral Medium 2506", "openai": {"id": "magistral-medium-2506", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Magistral is Mistral's first reasoning model. It is ideal for general purpose use requiring longer thought processing and better accuracy than with non-reasoning LLMs. From legal research and financial forecasting to software development and creative storytelling — this model solves multi-step challenges where transparency and precision are critical.", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "mist<PERSON><PERSON>", "profile_image_url": "/static/favicon.png"}}, "context_length": 40960, "reasoning_level": "light", "premium_model": false, "supports_function_calling": true, "actions": [], "tags": []}, {"id": "magistral-medium-2506:thinking", "object": "model", "created": **********, "owned_by": "mist<PERSON><PERSON>", "name": "Magistral Medium 2506 (thinking)", "openai": {"id": "magistral-medium-2506:thinking", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Magistral is Mistral's first reasoning model. It is ideal for general purpose use requiring longer thought processing and better accuracy than with non-reasoning LLMs. From legal research and financial forecasting to software development and creative storytelling — this model solves multi-step challenges where transparency and precision are critical.", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "mist<PERSON><PERSON>", "profile_image_url": "/static/favicon.png"}}, "context_length": 40960, "reasoning_level": "heavy", "premium_model": false, "supports_function_calling": true, "actions": [], "tags": []}, {"id": "magistral-small-2506", "object": "model", "created": **********, "owned_by": "mist<PERSON><PERSON>", "name": "Magistral Small 2506", "openai": {"id": "magistral-small-2506", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Magistral Small is a 24B parameter instruction-tuned model based on Mistral-Small-3.1 (2503), enhanced through supervised fine-tuning on traces from Magistral Medium and further refined via reinforcement learning. It is optimized for reasoning and supports a wide multilingual range, including over 20 languages.", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "mist<PERSON><PERSON>", "profile_image_url": "/static/favicon.png"}}, "context_length": 40000, "reasoning_level": "fast", "premium_model": false, "supports_function_calling": true, "actions": [], "tags": []}, {"id": "magnum-v2-72b", "object": "model", "created": **********, "owned_by": "anthracite-org", "name": "Magnum v2 72B", "openai": {"id": "magnum-v2-72b", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "From the maker of [Goliath](https://openrouter.ai/models/alpindale/goliath-120b), Magnum 72B is the seventh in a family of models designed to achieve the prose quality of the Claude 3 models, notably Opus & Sonnet.\n\nThe model is based on [Qwen2 72B](https://openrouter.ai/models/qwen/qwen-2-72b-instruct) and trained with 55 million tokens of highly curated roleplay (RP) data.", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "anthracite-org", "profile_image_url": "/static/favicon.png"}}, "context_length": 32768, "reasoning_level": "fast", "premium_model": false, "supports_function_calling": false, "actions": [], "tags": []}, {"id": "magnum-v4-72b", "object": "model", "created": **********, "owned_by": "anthracite-org", "name": "Magnum v4 72B", "openai": {"id": "magnum-v4-72b", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "This is a series of models designed to replicate the prose quality of the Claude 3 models, specifically Sonnet(https://openrouter.ai/anthropic/claude-3.5-sonnet) and Opus(https://openrouter.ai/anthropic/claude-3-opus).\n\nThe model is fine-tuned on top of [Qwen2.5 72B](https://openrouter.ai/qwen/qwen-2.5-72b-instruct).", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "anthracite-org", "profile_image_url": "/static/favicon.png"}}, "context_length": 16384, "reasoning_level": "fast", "premium_model": false, "supports_function_calling": false, "actions": [], "tags": []}, {"id": "ministral-3b", "object": "model", "created": **********, "owned_by": "mist<PERSON><PERSON>", "name": "Ministral 3B", "openai": {"id": "ministral-3b", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Ministral 3B is a 3B parameter model optimized for on-device and edge computing. It excels in knowledge, commonsense reasoning, and function-calling, outperforming larger models like Mistral 7B on most benchmarks. Supporting up to 128k context length, it’s ideal for orchestrating agentic workflows and specialist tasks with efficient inference.", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "mist<PERSON><PERSON>", "profile_image_url": "/static/favicon.png"}}, "context_length": 131072, "reasoning_level": "light", "premium_model": false, "supports_function_calling": true, "actions": [], "tags": []}, {"id": "ministral-8b", "object": "model", "created": **********, "owned_by": "mist<PERSON><PERSON>", "name": "Ministral 8B", "openai": {"id": "ministral-8b", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Ministral 8B is an 8B parameter model featuring a unique interleaved sliding-window attention pattern for faster, memory-efficient inference. Designed for edge use cases, it supports up to 128k context length and excels in knowledge and reasoning tasks. It outperforms peers in the sub-10B category, making it perfect for low-latency, privacy-first applications.", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "mist<PERSON><PERSON>", "profile_image_url": "/static/favicon.png"}}, "context_length": 128000, "reasoning_level": "light", "premium_model": false, "supports_function_calling": true, "actions": [], "tags": []}, {"id": "mistral-7b-instruct", "object": "model", "created": **********, "owned_by": "mist<PERSON><PERSON>", "name": "Mistral 7B Instruct", "openai": {"id": "mistral-7b-instruct", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "A high-performing, industry-standard 7.3B parameter model, with optimizations for speed and context length.\n\n*Mistral 7B Instruct has multiple version variants, and this is intended to be the latest version.*", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "mist<PERSON><PERSON>", "profile_image_url": "/static/favicon.png"}}, "context_length": 32768, "reasoning_level": "fast", "premium_model": false, "supports_function_calling": true, "actions": [], "tags": []}, {"id": "mistral-large", "object": "model", "created": **********, "owned_by": "mist<PERSON><PERSON>", "name": "Mistral Large", "openai": {"id": "mistral-large", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "This is Mistral AI's flagship model, Mistral Large 2 (version `mistral-large-2407`). It's a proprietary weights-available model and excels at reasoning, code, JSON, chat, and more. Read the launch announcement [here](https://mistral.ai/news/mistral-large-2407/).\n\nIt supports dozens of languages including French, German, Spanish, Italian, Portuguese, Arabic, Hindi, Russian, Chinese, Japanese, and Korean, along with 80+ coding languages including Python, Java, C, C++, JavaScript, and Bash. Its long context window allows precise information recall from large documents.", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "mist<PERSON><PERSON>", "profile_image_url": "/static/favicon.png"}}, "context_length": 128000, "reasoning_level": "fast", "premium_model": false, "supports_function_calling": true, "actions": [], "tags": []}, {"id": "mistral-medium-3", "object": "model", "created": **********, "owned_by": "mist<PERSON><PERSON>", "name": "Mistral Medium 3", "openai": {"id": "mistral-medium-3", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Mistral Medium 3 is a high-performance enterprise-grade language model designed to deliver frontier-level capabilities at significantly reduced operational cost. It balances state-of-the-art reasoning and multimodal performance with 8× lower cost compared to traditional large models, making it suitable for scalable deployments across professional and industrial use cases.\n\nThe model excels in domains such as coding, STEM reasoning, and enterprise adaptation. It supports hybrid, on-prem, and in-VPC deployments and is optimized for integration into custom workflows. Mistral Medium 3 offers competitive accuracy relative to larger models like Claude Sonnet 3.5/3.7, Llama 4 Maverick, and Command R+, while maintaining broad compatibility across cloud environments.", "modalities": {"input": ["text", "image"], "output": ["text"]}, "provider": "mist<PERSON><PERSON>", "profile_image_url": "/static/favicon.png"}}, "context_length": 131072, "reasoning_level": "fast", "premium_model": false, "supports_function_calling": true, "actions": [], "tags": []}, {"id": "mistral-medium-3.1", "object": "model", "created": **********, "owned_by": "mist<PERSON><PERSON>", "name": "Mistral Medium 3.1", "openai": {"id": "mistral-medium-3.1", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Mistral Medium 3.1 is an updated version of Mistral Medium 3, which is a high-performance enterprise-grade language model designed to deliver frontier-level capabilities at significantly reduced operational cost. It balances state-of-the-art reasoning and multimodal performance with 8× lower cost compared to traditional large models, making it suitable for scalable deployments across professional and industrial use cases.\n\nThe model excels in domains such as coding, STEM reasoning, and enterprise adaptation. It supports hybrid, on-prem, and in-VPC deployments and is optimized for integration into custom workflows. Mistral Medium 3.1 offers competitive accuracy relative to larger models like Claude Sonnet 3.5/3.7, Llama 4 Maverick, and Command R+, while maintaining broad compatibility across cloud environments.", "modalities": {"input": ["text", "image"], "output": ["text"]}, "provider": "mist<PERSON><PERSON>", "profile_image_url": "/static/favicon.png"}}, "context_length": 262144, "reasoning_level": "light", "premium_model": false, "supports_function_calling": true, "actions": [], "tags": []}, {"id": "mistral-nemo", "object": "model", "created": **********, "owned_by": "mist<PERSON><PERSON>", "name": "Mi<PERSON><PERSON> Nemo", "openai": {"id": "mistral-nemo", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "A 12B parameter model with a 128k token context length built by Mistral in collaboration with NVIDIA.\n\nThe model is multilingual, supporting English, French, German, Spanish, Italian, Portuguese, Chinese, Japanese, Korean, Arabic, and Hindi.\n\nIt supports function calling and is released under the Apache 2.0 license.", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "mist<PERSON><PERSON>", "profile_image_url": "/static/favicon.png"}}, "context_length": 131072, "reasoning_level": "fast", "premium_model": false, "supports_function_calling": true, "actions": [], "tags": []}, {"id": "mistral-small", "object": "model", "created": **********, "owned_by": "mist<PERSON><PERSON>", "name": "Mistra<PERSON> Small", "openai": {"id": "mistral-small", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "With 22 billion parameters, Mistral Small v24.09 offers a convenient mid-point between (Mistral NeMo 12B)[/mistralai/mistral-nemo] and (Mistral Large 2)[/mistralai/mistral-large], providing a cost-effective solution that can be deployed across various platforms and environments. It has better reasoning, exhibits more capabilities, can produce and reason about code, and is multiligual, supporting English, French, German, Italian, and Spanish.", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "mist<PERSON><PERSON>", "profile_image_url": "/static/favicon.png"}}, "context_length": 32768, "reasoning_level": "fast", "premium_model": false, "supports_function_calling": true, "actions": [], "tags": []}, {"id": "mistral-small-24b-instruct-2501", "object": "model", "created": **********, "owned_by": "mist<PERSON><PERSON>", "name": "Mistral Small 3", "openai": {"id": "mistral-small-24b-instruct-2501", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Mistral Small 3 is a 24B-parameter language model optimized for low-latency performance across common AI tasks. Released under the Apache 2.0 license, it features both pre-trained and instruction-tuned versions designed for efficient local deployment.\n\nThe model achieves 81% accuracy on the MMLU benchmark and performs competitively with larger models like Llama 3.3 70B and Qwen 32B, while operating at three times the speed on equivalent hardware. [Read the blog post about the model here.](https://mistral.ai/news/mistral-small-3/)", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "mist<PERSON><PERSON>", "profile_image_url": "/static/favicon.png"}}, "context_length": 32768, "reasoning_level": "fast", "premium_model": false, "supports_function_calling": true, "actions": [], "tags": []}, {"id": "mistral-small-3.1-24b-instruct", "object": "model", "created": **********, "owned_by": "mist<PERSON><PERSON>", "name": "Mistral Small 3.1 24B", "openai": {"id": "mistral-small-3.1-24b-instruct", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Mistral Small 3.1 24B Instruct is an upgraded variant of Mistral Small 3 (2501), featuring 24 billion parameters with advanced multimodal capabilities. It provides state-of-the-art performance in text-based reasoning and vision tasks, including image analysis, programming, mathematical reasoning, and multilingual support across dozens of languages. Equipped with an extensive 128k token context window and optimized for efficient local inference, it supports use cases such as conversational agents, function calling, long-document comprehension, and privacy-sensitive deployments.", "modalities": {"input": ["text", "image"], "output": ["text"]}, "provider": "mist<PERSON><PERSON>", "profile_image_url": "/static/favicon.png"}}, "context_length": 32768, "reasoning_level": "fast", "premium_model": false, "supports_function_calling": true, "actions": [], "tags": []}, {"id": "mistral-small-3.2-24b-instruct", "object": "model", "created": **********, "owned_by": "mist<PERSON><PERSON>", "name": "Mistral Small 3.2 24B", "openai": {"id": "mistral-small-3.2-24b-instruct", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Mistral-Small-3.2-24B-Instruct-2506 is an updated 24B parameter model from Mistral optimized for instruction following, repetition reduction, and improved function calling. Compared to the 3.1 release, version 3.2 significantly improves accuracy on WildBench and Arena Hard, reduces infinite generations, and delivers gains in tool use and structured output tasks.\n\nIt supports image and text inputs with structured outputs, function/tool calling, and strong performance across coding (HumanEval+, MBPP), STEM (MMLU, MATH, GPQA), and vision benchmarks (ChartQA, DocVQA).", "modalities": {"input": ["image", "text"], "output": ["text"]}, "provider": "mist<PERSON><PERSON>", "profile_image_url": "/static/favicon.png"}}, "context_length": 131072, "reasoning_level": "fast", "premium_model": false, "supports_function_calling": true, "actions": [], "tags": []}, {"id": "mistral-tiny", "object": "model", "created": **********, "owned_by": "mist<PERSON><PERSON>", "name": "Mistral Tiny", "openai": {"id": "mistral-tiny", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Note: This model is being deprecated. Recommended replacement is the newer [Ministral 8B](/mistral/ministral-8b)\n\nThis model is currently powered by Mistral-7B-v0.2, and incorporates a \"better\" fine-tuning than [Mistral 7B](/models/mistralai/mistral-7b-instruct-v0.1), inspired by community work. It's best used for large batch processing tasks where cost is a significant factor but reasoning capabilities are not crucial.", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "mist<PERSON><PERSON>", "profile_image_url": "/static/favicon.png"}}, "context_length": 32768, "reasoning_level": "fast", "premium_model": false, "supports_function_calling": true, "actions": [], "tags": []}, {"id": "mixtral-8x22b-instruct", "object": "model", "created": **********, "owned_by": "mist<PERSON><PERSON>", "name": "Mixtral 8x22B Instruct", "openai": {"id": "mixtral-8x22b-instruct", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Mistral's official instruct fine-tuned version of [Mixtral 8x22B](/models/mistralai/mixtral-8x22b). It uses 39B active parameters out of 141B, offering unparalleled cost efficiency for its size. Its strengths include:\n- strong math, coding, and reasoning\n- large context length (64k)\n- fluency in English, French, Italian, German, and Spanish\n\nSee benchmarks on the launch announcement [here](https://mistral.ai/news/mixtral-8x22b/).\n#moe", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "mist<PERSON><PERSON>", "profile_image_url": "/static/favicon.png"}}, "context_length": 65536, "reasoning_level": "fast", "premium_model": false, "supports_function_calling": true, "actions": [], "tags": []}, {"id": "mixtral-8x7b-instruct", "object": "model", "created": **********, "owned_by": "mist<PERSON><PERSON>", "name": "Mixtral 8x7B Instruct", "openai": {"id": "mixtral-8x7b-instruct", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Mixtral 8x7B Instruct is a pretrained generative Sparse Mixture of Experts, by Mistral AI, for chat and instruction use. Incorporates 8 experts (feed-forward networks) for a total of 47 billion parameters.\n\nInstruct model fine-tuned by Mistral. #moe", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "mist<PERSON><PERSON>", "profile_image_url": "/static/favicon.png"}}, "context_length": 32768, "reasoning_level": "fast", "premium_model": false, "supports_function_calling": true, "actions": [], "tags": []}, {"id": "nova-lite-v1", "object": "model", "created": **********, "owned_by": "amazon", "name": "Nova Lite 1.0", "openai": {"id": "nova-lite-v1", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Amazon Nova Lite 1.0 is a very low-cost multimodal model from Amazon that focused on fast processing of image, video, and text inputs to generate text output. Amazon Nova Lite can handle real-time customer interactions, document analysis, and visual question-answering tasks with high accuracy.\n\nWith an input context of 300K tokens, it can analyze multiple images or up to 30 minutes of video in a single input.", "modalities": {"input": ["text", "image"], "output": ["text"]}, "provider": "amazon", "profile_image_url": "/static/amazon.png"}}, "context_length": 300000, "reasoning_level": "fast", "premium_model": false, "supports_function_calling": true, "actions": [], "tags": []}, {"id": "nova-micro-v1", "object": "model", "created": **********, "owned_by": "amazon", "name": "Nova Micro 1.0", "openai": {"id": "nova-micro-v1", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Amazon Nova Micro 1.0 is a text-only model that delivers the lowest latency responses in the Amazon Nova family of models at a very low cost. With a context length of 128K tokens and optimized for speed and cost, Amazon Nova Micro excels at tasks such as text summarization, translation, content classification, interactive chat, and brainstorming. It has  simple mathematical reasoning and coding abilities.", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "amazon", "profile_image_url": "/static/amazon.png"}}, "context_length": 128000, "reasoning_level": "fast", "premium_model": false, "supports_function_calling": true, "actions": [], "tags": []}, {"id": "nova-pro-v1", "object": "model", "created": **********, "owned_by": "amazon", "name": "Nova Pro 1.0", "openai": {"id": "nova-pro-v1", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Amazon Nova Pro 1.0 is a capable multimodal model from Amazon focused on providing a combination of accuracy, speed, and cost for a wide range of tasks. As of December 2024, it achieves state-of-the-art performance on key benchmarks including visual question answering (TextVQA) and video understanding (VATEX).\n\nAmazon Nova Pro demonstrates strong capabilities in processing both visual and textual information and at analyzing financial documents.\n\n**NOTE**: Video input is not supported at this time.", "modalities": {"input": ["text", "image"], "output": ["text"]}, "provider": "amazon", "profile_image_url": "/static/amazon.png"}}, "context_length": 300000, "reasoning_level": "fast", "premium_model": false, "supports_function_calling": true, "actions": [], "tags": []}, {"id": "phi-4", "object": "model", "created": **********, "owned_by": "microsoft", "name": "Phi 4", "openai": {"id": "phi-4", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "[Microsoft Research](/microsoft) Phi-4 is designed to perform well in complex reasoning tasks and can operate efficiently in situations with limited memory or where quick responses are needed. \n\nAt 14 billion parameters, it was trained on a mix of high-quality synthetic datasets, data from curated websites, and academic materials. It has undergone careful improvement to follow instructions accurately and maintain strong safety standards. It works best with English language inputs.\n\nFor more information, please see [Phi-4 Technical Report](https://arxiv.org/pdf/2412.08905)\n", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "microsoft", "profile_image_url": "/static/microsoft.png"}}, "context_length": 16384, "reasoning_level": "light", "premium_model": false, "supports_function_calling": false, "actions": [], "tags": []}, {"id": "phi-4-multimodal-instruct", "object": "model", "created": **********, "owned_by": "microsoft", "name": "Phi 4 Multimodal Instruct", "openai": {"id": "phi-4-multimodal-instruct", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Phi-4 Multimodal Instruct is a versatile 5.6B parameter foundation model that combines advanced reasoning and instruction-following capabilities across both text and visual inputs, providing accurate text outputs. The unified architecture enables efficient, low-latency inference, suitable for edge and mobile deployments. Phi-4 Multimodal Instruct supports text inputs in multiple languages including Arabic, Chinese, English, French, German, Japanese, Spanish, and more, with visual input optimized primarily for English. It delivers impressive performance on multimodal tasks involving mathematical, scientific, and document reasoning, providing developers and enterprises a powerful yet compact model for sophisticated interactive applications. For more information, see the [Phi-4 Multimodal blog post](https://azure.microsoft.com/en-us/blog/empowering-innovation-the-next-generation-of-the-phi-family/).\n", "modalities": {"input": ["text", "image"], "output": ["text"]}, "provider": "microsoft", "profile_image_url": "/static/microsoft.png"}}, "context_length": 131072, "reasoning_level": "fast", "premium_model": false, "supports_function_calling": false, "actions": [], "tags": []}, {"id": "phi-4-reasoning-plus", "object": "model", "created": **********, "owned_by": "microsoft", "name": "Phi 4 Reasoning Plus", "openai": {"id": "phi-4-reasoning-plus", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Phi-4-reasoning-plus is an enhanced 14B parameter model from Microsoft, fine-tuned from Phi-4 with additional reinforcement learning to boost accuracy on math, science, and code reasoning tasks. It uses the same dense decoder-only transformer architecture as Phi-4, but generates longer, more comprehensive outputs structured into a step-by-step reasoning trace and final answer.\n\nWhile it offers improved benchmark scores over Phi-4-reasoning across tasks like AIME, OmniMath, and HumanEvalPlus, its responses are typically ~50% longer, resulting in higher latency. Designed for English-only applications, it is well-suited for structured reasoning workflows where output quality takes priority over response speed.", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "microsoft", "profile_image_url": "/static/microsoft.png"}}, "context_length": 32768, "reasoning_level": "heavy", "premium_model": false, "supports_function_calling": false, "actions": [], "tags": []}, {"id": "phi-3-medium-128k-instruct", "object": "model", "created": **********, "owned_by": "microsoft", "name": "Phi-3 Medium 128K Instruct", "openai": {"id": "phi-3-medium-128k-instruct", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Phi-3 128K Medium is a powerful 14-billion parameter model designed for advanced language understanding, reasoning, and instruction following. Optimized through supervised fine-tuning and preference adjustments, it excels in tasks involving common sense, mathematics, logical reasoning, and code processing.\n\nAt time of release, Phi-3 Medium demonstrated state-of-the-art performance among lightweight models. In the MMLU-Pro eval, the model even comes close to a Llama3 70B level of performance.\n\nFor 4k context length, try [Phi-3 Medium 4K](/models/microsoft/phi-3-medium-4k-instruct).", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "microsoft", "profile_image_url": "/static/microsoft.png"}}, "context_length": 128000, "reasoning_level": "fast", "premium_model": false, "supports_function_calling": true, "actions": [], "tags": []}, {"id": "phi-3.5-mini-128k-instruct", "object": "model", "created": **********, "owned_by": "microsoft", "name": "Phi-3.5 Mini 128K Instruct", "openai": {"id": "phi-3.5-mini-128k-instruct", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Phi-3.5 models are lightweight, state-of-the-art open models. These models were trained with Phi-3 datasets that include both synthetic data and the filtered, publicly available websites data, with a focus on high quality and reasoning-dense properties. Phi-3.5 Mini uses 3.8B parameters, and is a dense decoder-only transformer model using the same tokenizer as [Phi-3 Mini](/models/microsoft/phi-3-mini-128k-instruct).\n\nThe models underwent a rigorous enhancement process, incorporating both supervised fine-tuning, proximal policy optimization, and direct preference optimization to ensure precise instruction adherence and robust safety measures. When assessed against benchmarks that test common sense, language understanding, math, code, long context and logical reasoning, Phi-3.5 models showcased robust and state-of-the-art performance among models with less than 13 billion parameters.", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "microsoft", "profile_image_url": "/static/microsoft.png"}}, "context_length": 128000, "reasoning_level": "fast", "premium_model": false, "supports_function_calling": true, "actions": [], "tags": []}, {"id": "pixtral-12b", "object": "model", "created": **********, "owned_by": "mist<PERSON><PERSON>", "name": "Pixtral 12B", "openai": {"id": "pixtral-12b", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "The first multi-modal, text+image-to-text model from Mistral AI. Its weights were launched via torrent: https://x.com/mistralai/status/1833758285167722836.", "modalities": {"input": ["text", "image"], "output": ["text"]}, "provider": "mist<PERSON><PERSON>", "profile_image_url": "/static/favicon.png"}}, "context_length": 32768, "reasoning_level": "fast", "premium_model": false, "supports_function_calling": true, "actions": [], "tags": []}, {"id": "pixtral-large-2411", "object": "model", "created": **********, "owned_by": "mist<PERSON><PERSON>", "name": "Pixtral Large 2411", "openai": {"id": "pixtral-large-2411", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Pixtral Large is a 124B parameter, open-weight, multimodal model built on top of [Mistral Large 2](/mistralai/mistral-large-2411). The model is able to understand documents, charts and natural images.\n\nThe model is available under the Mistral Research License (MRL) for research and educational use, and the Mistral Commercial License for experimentation, testing, and production for commercial purposes.", "modalities": {"input": ["text", "image"], "output": ["text"]}, "provider": "mist<PERSON><PERSON>", "profile_image_url": "/static/favicon.png"}}, "context_length": 131072, "reasoning_level": "fast", "premium_model": false, "supports_function_calling": true, "actions": [], "tags": []}, {"id": "qwq-32b", "object": "model", "created": **********, "owned_by": "qwen", "name": "QwQ 32B", "openai": {"id": "qwq-32b", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "QwQ is the reasoning model of the Qwen series. Compared with conventional instruction-tuned models, QwQ, which is capable of thinking and reasoning, can achieve significantly enhanced performance in downstream tasks, especially hard problems. QwQ-32B is the medium-sized reasoning model, which is capable of achieving competitive performance against state-of-the-art reasoning models, e.g., DeepSeek-R1, o1-mini.", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "qwen", "profile_image_url": "/static/qwen.png"}}, "context_length": 131072, "reasoning_level": "heavy", "premium_model": false, "supports_function_calling": false, "actions": [], "tags": []}, {"id": "qwq-32b-arliai-rpr-v1", "object": "model", "created": **********, "owned_by": "<PERSON><PERSON><PERSON><PERSON>", "name": "QwQ 32B RpR v1", "openai": {"id": "qwq-32b-arliai-rpr-v1", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "QwQ-32B-ArliAI-RpR-v1 is a 32B parameter model fine-tuned from Qwen/QwQ-32B using a curated creative writing and roleplay dataset originally developed for the RPMax series. It is designed to maintain coherence and reasoning across long multi-turn conversations by introducing explicit reasoning steps per dialogue turn, generated and refined using the base model itself.\n\nThe model was trained using RS-QLORA+ on 8K sequence lengths and supports up to 128K context windows (with practical performance around 32K). It is optimized for creative roleplay and dialogue generation, with an emphasis on minimizing cross-context repetition while preserving stylistic diversity.", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "<PERSON><PERSON><PERSON><PERSON>", "profile_image_url": "/static/favicon.png"}}, "context_length": 32768, "reasoning_level": "light", "premium_model": false, "supports_function_calling": false, "actions": [], "tags": []}, {"id": "qwen-2.5-72b-instruct", "object": "model", "created": **********, "owned_by": "qwen", "name": "Qwen2.5 72B Instruct", "openai": {"id": "qwen-2.5-72b-instruct", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Qwen2.5 72B is the latest series of Qwen large language models. Qwen2.5 brings the following improvements upon Qwen2:\n\n- Significantly more knowledge and has greatly improved capabilities in coding and mathematics, thanks to our specialized expert models in these domains.\n\n- Significant improvements in instruction following, generating long texts (over 8K tokens), understanding structured data (e.g, tables), and generating structured outputs especially JSON. More resilient to the diversity of system prompts, enhancing role-play implementation and condition-setting for chatbots.\n\n- Long-context Support up to 128K tokens and can generate up to 8K tokens.\n\n- Multilingual support for over 29 languages, including Chinese, English, French, Spanish, Portuguese, German, Italian, Russian, Japanese, Korean, Vietnamese, Thai, Arabic, and more.\n\nUsage of this model is subject to [Tongyi Qianwen LICENSE AGREEMENT](https://huggingface.co/Qwen/Qwen1.5-110B-Chat/blob/main/LICENSE).", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "qwen", "profile_image_url": "/static/qwen.png"}}, "context_length": 131072, "reasoning_level": "fast", "premium_model": false, "supports_function_calling": true, "actions": [], "tags": []}, {"id": "qwen-2.5-7b-instruct", "object": "model", "created": **********, "owned_by": "qwen", "name": "Qwen2.5 7B Instruct", "openai": {"id": "qwen-2.5-7b-instruct", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Qwen2.5 7B is the latest series of Qwen large language models. Qwen2.5 brings the following improvements upon Qwen2:\n\n- Significantly more knowledge and has greatly improved capabilities in coding and mathematics, thanks to our specialized expert models in these domains.\n\n- Significant improvements in instruction following, generating long texts (over 8K tokens), understanding structured data (e.g, tables), and generating structured outputs especially JSON. More resilient to the diversity of system prompts, enhancing role-play implementation and condition-setting for chatbots.\n\n- Long-context Support up to 128K tokens and can generate up to 8K tokens.\n\n- Multilingual support for over 29 languages, including Chinese, English, French, Spanish, Portuguese, German, Italian, Russian, Japanese, Korean, Vietnamese, Thai, Arabic, and more.\n\nUsage of this model is subject to [Tongyi Qianwen LICENSE AGREEMENT](https://huggingface.co/Qwen/Qwen1.5-110B-Chat/blob/main/LICENSE).", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "qwen", "profile_image_url": "/static/qwen.png"}}, "context_length": 32768, "reasoning_level": "fast", "premium_model": false, "supports_function_calling": false, "actions": [], "tags": []}, {"id": "qwen-2.5-coder-32b-instruct", "object": "model", "created": **********, "owned_by": "qwen", "name": "Qwen2.5 Coder 32B Instruct", "openai": {"id": "qwen-2.5-coder-32b-instruct", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Qwen2.5-Coder is the latest series of Code-Specific Qwen large language models (formerly known as CodeQwen). Qwen2.5-Coder brings the following improvements upon CodeQwen1.5:\n\n- Significantly improvements in **code generation**, **code reasoning** and **code fixing**. \n- A more comprehensive foundation for real-world applications such as **Code Agents**. Not only enhancing coding capabilities but also maintaining its strengths in mathematics and general competencies.\n\nTo read more about its evaluation results, check out [<PERSON>wen 2.5 Coder's blog](https://qwenlm.github.io/blog/qwen2.5-coder-family/).", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "qwen", "profile_image_url": "/static/qwen.png"}}, "context_length": 33000, "reasoning_level": "fast", "premium_model": false, "supports_function_calling": false, "actions": [], "tags": []}, {"id": "qwen2.5-vl-72b-instruct", "object": "model", "created": **********, "owned_by": "qwen", "name": "Qwen2.5 VL 72B Instruct", "openai": {"id": "qwen2.5-vl-72b-instruct", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Qwen2.5-VL is proficient in recognizing common objects such as flowers, birds, fish, and insects. It is also highly capable of analyzing texts, charts, icons, graphics, and layouts within images.", "modalities": {"input": ["text", "image"], "output": ["text"]}, "provider": "qwen", "profile_image_url": "/static/qwen.png"}}, "context_length": 128000, "reasoning_level": "fast", "premium_model": false, "supports_function_calling": false, "actions": [], "tags": []}, {"id": "qwen-2.5-vl-7b-instruct", "object": "model", "created": **********, "owned_by": "qwen", "name": "Qwen2.5-VL 7B Instruct", "openai": {"id": "qwen-2.5-vl-7b-instruct", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Qwen2.5 VL 7B is a multimodal LLM from the Qwen Team with the following key enhancements:\n\n- SoTA understanding of images of various resolution & ratio: Qwen2.5-VL achieves state-of-the-art performance on visual understanding benchmarks, including MathVista, DocVQA, RealWorldQA, MTVQA, etc.\n\n- Understanding videos of 20min+: Qwen2.5-<PERSON><PERSON> can understand videos over 20 minutes for high-quality video-based question answering, dialog, content creation, etc.\n\n- Agent that can operate your mobiles, robots, etc.: with the abilities of complex reasoning and decision making, Qwen2.5-VL can be integrated with devices like mobile phones, robots, etc., for automatic operation based on visual environment and text instructions.\n\n- Multilingual Support: to serve global users, besides English and Chinese, Qwen2.5-VL now supports the understanding of texts in different languages inside images, including most European languages, Japanese, Korean, Arabic, Vietnamese, etc.\n\nFor more details, see this [blog post](https://qwenlm.github.io/blog/qwen2-vl/) and [GitHub repo](https://github.com/QwenLM/Qwen2-VL).\n\nUsage of this model is subject to [Tongyi Qianwen LICENSE AGREEMENT](https://huggingface.co/Qwen/Qwen1.5-110B-Chat/blob/main/LICENSE).", "modalities": {"input": ["text", "image"], "output": ["text"]}, "provider": "qwen", "profile_image_url": "/static/qwen.png"}}, "context_length": 32768, "reasoning_level": "fast", "premium_model": false, "supports_function_calling": false, "actions": [], "tags": []}, {"id": "qwen3-14b", "object": "model", "created": **********, "owned_by": "qwen", "name": "Qwen3 14B", "openai": {"id": "qwen3-14b", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Qwen3-14B is a dense 14.8B parameter causal language model from the Qwen3 series, designed for both complex reasoning and efficient dialogue. It supports seamless switching between a \"thinking\" mode for tasks like math, programming, and logical inference, and a \"non-thinking\" mode for general-purpose conversation. The model is fine-tuned for instruction-following, agent tool use, creative writing, and multilingual tasks across 100+ languages and dialects. It natively handles 32K token contexts and can extend to 131K tokens using YaRN-based scaling.", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "qwen", "profile_image_url": "/static/qwen.png"}}, "context_length": 40960, "reasoning_level": "fast", "premium_model": false, "supports_function_calling": true, "actions": [], "tags": []}, {"id": "qwen3-235b-a22b", "object": "model", "created": **********, "owned_by": "qwen", "name": "Qwen3 235B A22B", "openai": {"id": "qwen3-235b-a22b", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Qwen3-235B-A22B is a 235B parameter mixture-of-experts (MoE) model developed by Qwen, activating 22B parameters per forward pass. It supports seamless switching between a \"thinking\" mode for complex reasoning, math, and code tasks, and a \"non-thinking\" mode for general conversational efficiency. The model demonstrates strong reasoning ability, multilingual support (100+ languages and dialects), advanced instruction-following, and agent tool-calling capabilities. It natively handles a 32K token context window and extends up to 131K tokens using YaRN-based scaling.", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "qwen", "profile_image_url": "/static/qwen.png"}}, "context_length": 32768, "reasoning_level": "fast", "premium_model": false, "supports_function_calling": true, "actions": [], "tags": []}, {"id": "qwen3-235b-a22b-2507", "object": "model", "created": **********, "owned_by": "qwen", "name": "Qwen3 235B A22B Instruct 2507", "openai": {"id": "qwen3-235b-a22b-2507", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Qwen3-235B-A22B-Instruct-2507 is a multilingual, instruction-tuned mixture-of-experts language model based on the Qwen3-235B architecture, with 22B active parameters per forward pass. It is optimized for general-purpose text generation, including instruction following, logical reasoning, math, code, and tool usage. The model supports a native 262K context length and does not implement \"thinking mode\" (<think> blocks).\n\nCompared to its base variant, this version delivers significant gains in knowledge coverage, long-context reasoning, coding benchmarks, and alignment with open-ended tasks. It is particularly strong on multilingual understanding, math reasoning (e.g., AIME, HMMT), and alignment evaluations like Arena-Hard and WritingBench.", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "qwen", "profile_image_url": "/static/qwen.png"}}, "context_length": 262144, "reasoning_level": "heavy", "premium_model": false, "supports_function_calling": true, "actions": [], "tags": []}, {"id": "qwen3-235b-a22b-thinking-2507", "object": "model", "created": **********, "owned_by": "qwen", "name": "Qwen3 235B A22B Thinking 2507", "openai": {"id": "qwen3-235b-a22b-thinking-2507", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Qwen3-235B-A22B-Thinking-2507 is a high-performance, open-weight Mixture-of-Experts (MoE) language model optimized for complex reasoning tasks. It activates 22B of its 235B parameters per forward pass and natively supports up to 262,144 tokens of context. This \"thinking-only\" variant enhances structured logical reasoning, mathematics, science, and long-form generation, showing strong benchmark performance across AIME, SuperGPQA, LiveCodeBench, and MMLU-Redux. It enforces a special reasoning mode (</think>) and is designed for high-token outputs (up to 81,920 tokens) in challenging domains.\n\nThe model is instruction-tuned and excels at step-by-step reasoning, tool use, agentic workflows, and multilingual tasks. This release represents the most capable open-source variant in the Qwen3-235B series, surpassing many closed models in structured reasoning use cases.", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "qwen", "profile_image_url": "/static/qwen.png"}}, "context_length": 262144, "reasoning_level": "heavy", "premium_model": false, "supports_function_calling": true, "actions": [], "tags": []}, {"id": "qwen3-30b-a3b", "object": "model", "created": **********, "owned_by": "qwen", "name": "Qwen3 30B A3B", "openai": {"id": "qwen3-30b-a3b", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Qwen3, the latest generation in the Qwen large language model series, features both dense and mixture-of-experts (MoE) architectures to excel in reasoning, multilingual support, and advanced agent tasks. Its unique ability to switch seamlessly between a thinking mode for complex reasoning and a non-thinking mode for efficient dialogue ensures versatile, high-quality performance.\n\nSignificantly outperforming prior models like QwQ and Qwen2.5, Qwen3 delivers superior mathematics, coding, commonsense reasoning, creative writing, and interactive dialogue capabilities. The Qwen3-30B-A3B variant includes 30.5 billion parameters (3.3 billion activated), 48 layers, 128 experts (8 activated per task), and supports up to 131K token contexts with YaRN, setting a new standard among open-source models.", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "qwen", "profile_image_url": "/static/qwen.png"}}, "context_length": 40960, "reasoning_level": "fast", "premium_model": false, "supports_function_calling": true, "actions": [], "tags": []}, {"id": "qwen3-30b-a3b-instruct-2507", "object": "model", "created": **********, "owned_by": "qwen", "name": "Qwen3 30B A3B Instruct 2507", "openai": {"id": "qwen3-30b-a3b-instruct-2507", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Qwen3-30B-A3B-Instruct-2507 is a 30.5B-parameter mixture-of-experts language model from Qwen, with 3.3B active parameters per inference. It operates in non-thinking mode and is designed for high-quality instruction following, multilingual understanding, and agentic tool use. Post-trained on instruction data, it demonstrates competitive performance across reasoning (AIME, ZebraLogic), coding (MultiPL-E, LiveCodeBench), and alignment (IFEval, WritingBench) benchmarks. It outperforms its non-instruct variant on subjective and open-ended tasks while retaining strong factual and coding performance.", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "qwen", "profile_image_url": "/static/qwen.png"}}, "context_length": 131072, "reasoning_level": "light", "premium_model": false, "supports_function_calling": false, "actions": [], "tags": []}, {"id": "qwen3-32b", "object": "model", "created": **********, "owned_by": "qwen", "name": "Qwen3 32B", "openai": {"id": "qwen3-32b", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Qwen3-32B is a dense 32.8B parameter causal language model from the Qwen3 series, optimized for both complex reasoning and efficient dialogue. It supports seamless switching between a \"thinking\" mode for tasks like math, coding, and logical inference, and a \"non-thinking\" mode for faster, general-purpose conversation. The model demonstrates strong performance in instruction-following, agent tool use, creative writing, and multilingual tasks across 100+ languages and dialects. It natively handles 32K token contexts and can extend to 131K tokens using YaRN-based scaling. ", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "qwen", "profile_image_url": "/static/qwen.png"}}, "context_length": 40960, "reasoning_level": "fast", "premium_model": false, "supports_function_calling": true, "actions": [], "tags": []}, {"id": "qwen3-8b", "object": "model", "created": **********, "owned_by": "qwen", "name": "Qwen3 8B", "openai": {"id": "qwen3-8b", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Qwen3-8B is a dense 8.2B parameter causal language model from the Qwen3 series, designed for both reasoning-heavy tasks and efficient dialogue. It supports seamless switching between \"thinking\" mode for math, coding, and logical inference, and \"non-thinking\" mode for general conversation. The model is fine-tuned for instruction-following, agent integration, creative writing, and multilingual use across 100+ languages and dialects. It natively supports a 32K token context window and can extend to 131K tokens with YaRN scaling.", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "qwen", "profile_image_url": "/static/qwen.png"}}, "context_length": 128000, "reasoning_level": "fast", "premium_model": false, "supports_function_calling": false, "actions": [], "tags": []}, {"id": "qwen3-coder", "object": "model", "created": **********, "owned_by": "qwen", "name": "Qwen3 Coder ", "openai": {"id": "qwen3-coder", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Qwen3-Coder-480B-A35B-Instruct is a Mixture-of-Experts (MoE) code generation model developed by the Qwen team. It is optimized for agentic coding tasks such as function calling, tool use, and long-context reasoning over repositories. The model features 480 billion total parameters, with 35 billion active per forward pass (8 out of 160 experts).\n\nPricing for the Alibaba endpoints varies by context length. Once a request is greater than 128k input tokens, the higher pricing is used.", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "qwen", "profile_image_url": "/static/qwen.png"}}, "context_length": 262144, "reasoning_level": "light", "premium_model": false, "supports_function_calling": true, "actions": [], "tags": []}, {"id": "deepseek-r1", "object": "model", "created": **********, "owned_by": "deepseek", "name": "R1", "openai": {"id": "deepseek-r1", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "DeepSeek R1 is here: Performance on par with [OpenAI o1](/openai/o1), but open-sourced and with fully open reasoning tokens. It's 671B parameters in size, with 37B active in an inference pass.\n\nFully open-source model & [technical report](https://api-docs.deepseek.com/news/news250120).\n\nMIT licensed: Distill & commercialize freely!", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "deepseek", "profile_image_url": "/static/deepseek.png"}}, "context_length": 163840, "reasoning_level": "fast", "premium_model": false, "supports_function_calling": true, "actions": [], "tags": []}, {"id": "r1-1776", "object": "model", "created": **********, "owned_by": "perplexity", "name": "R1 1776", "openai": {"id": "r1-1776", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "R1 1776 is a version of DeepSeek-R1 that has been post-trained to remove censorship constraints related to topics restricted by the Chinese government. The model retains its original reasoning capabilities while providing direct responses to a wider range of queries. R1 1776 is an offline chat model that does not use the perplexity search subsystem.\n\nThe model was tested on a multilingual dataset of over 1,000 examples covering sensitive topics to measure its likelihood of refusal or overly filtered responses. [Evaluation Results](https://cdn-uploads.huggingface.co/production/uploads/675c8332d01f593dc90817f5/GiN2VqC5hawUgAGJ6oHla.png) Its performance on math and reasoning benchmarks remains similar to the base R1 model. [Reasoning Performance](https://cdn-uploads.huggingface.co/production/uploads/675c8332d01f593dc90817f5/n4Z9Byqp2S7sKUvCvI40R.png)\n\nRead more on the [Blog Post](https://perplexity.ai/hub/blog/open-sourcing-r1-1776)", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "perplexity", "profile_image_url": "/static/perplexity.png"}}, "context_length": 128000, "reasoning_level": "fast", "premium_model": false, "supports_function_calling": false, "actions": [], "tags": []}, {"id": "deepseek-r1-distill-llama-8b", "object": "model", "created": **********, "owned_by": "deepseek", "name": "R1 Distill Llama 8B", "openai": {"id": "deepseek-r1-distill-llama-8b", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "DeepSeek R1 Distill Llama 8B is a distilled large language model based on [Llama-3.1-8B-Instruct](/meta-llama/llama-3.1-8b-instruct), using outputs from [DeepSeek R1](/deepseek/deepseek-r1). The model combines advanced distillation techniques to achieve high performance across multiple benchmarks, including:\n\n- AIME 2024 pass@1: 50.4\n- MATH-500 pass@1: 89.1\n- CodeForces Rating: 1205\n\nThe model leverages fine-tuning from DeepSeek R1's outputs, enabling competitive performance comparable to larger frontier models.\n\nHugging Face: \n- [Llama-3.1-8B](https://huggingface.co/meta-llama/Llama-3.1-8B) \n- [DeepSeek-R1-Distill-Llama-8B](https://huggingface.co/deepseek-ai/DeepSeek-R1-Distill-Llama-8B)   |", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "deepseek", "profile_image_url": "/static/deepseek.png"}}, "context_length": 32000, "reasoning_level": "fast", "premium_model": false, "supports_function_calling": false, "actions": [], "tags": []}, {"id": "deepseek-r1-distill-qwen-14b", "object": "model", "created": **********, "owned_by": "deepseek", "name": "R1 Distill Qwen 14B", "openai": {"id": "deepseek-r1-distill-qwen-14b", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "DeepSeek R1 Distill Qwen 14B is a distilled large language model based on [Qwen 2.5 14B](https://huggingface.co/deepseek-ai/DeepSeek-R1-Distill-Qwen-14B), using outputs from [DeepSeek R1](/deepseek/deepseek-r1). It outperforms OpenAI's o1-mini across various benchmarks, achieving new state-of-the-art results for dense models.\n\nOther benchmark results include:\n\n- AIME 2024 pass@1: 69.7\n- MATH-500 pass@1: 93.9\n- CodeForces Rating: 1481\n\nThe model leverages fine-tuning from DeepSeek R1's outputs, enabling competitive performance comparable to larger frontier models.", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "deepseek", "profile_image_url": "/static/deepseek.png"}}, "context_length": 64000, "reasoning_level": "fast", "premium_model": false, "supports_function_calling": false, "actions": [], "tags": []}, {"id": "mistral-saba", "object": "model", "created": **********, "owned_by": "mist<PERSON><PERSON>", "name": "Saba", "openai": {"id": "mistral-saba", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Mistral Saba is a 24B-parameter language model specifically designed for the Middle East and South Asia, delivering accurate and contextually relevant responses while maintaining efficient performance. Trained on curated regional datasets, it supports multiple Indian-origin languages—including Tamil and Malayalam—alongside Arabic. This makes it a versatile option for a range of regional and multilingual applications. Read more at the blog post [here](https://mistral.ai/en/news/mistral-saba)", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "mist<PERSON><PERSON>", "profile_image_url": "/static/favicon.png"}}, "context_length": 32768, "reasoning_level": "fast", "premium_model": false, "supports_function_calling": true, "actions": [], "tags": []}, {"id": "shisa-v2-llama3.3-70b", "object": "model", "created": **********, "owned_by": "shisa-ai", "name": "Shisa V2 Llama 3.3 70B ", "openai": {"id": "shisa-v2-llama3.3-70b", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Shisa V2 Llama 3.3 70B is a bilingual Japanese-English chat model fine-tuned by Shisa.AI on Meta’s Llama-3.3-70B-Instruct base. It prioritizes Japanese language performance while retaining strong English capabilities. The model was optimized entirely through post-training, using a refined mix of supervised fine-tuning (SFT) and DPO datasets including regenerated ShareGPT-style data, translation tasks, roleplaying conversations, and instruction-following prompts. Unlike earlier Shisa releases, this version avoids tokenizer modifications or extended pretraining.\n\nShisa V2 70B achieves leading Japanese task performance across a wide range of custom and public benchmarks, including JA MT Bench, ELYZA 100, and Rakuda. It supports a 128K token context length and integrates smoothly with inference frameworks like vLLM and SGLang. While it inherits safety characteristics from its base model, no additional alignment was applied. The model is intended for high-performance bilingual chat, instruction following, and translation tasks across JA/EN.", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "shisa-ai", "profile_image_url": "/static/favicon.png"}}, "context_length": 32768, "reasoning_level": "light", "premium_model": false, "supports_function_calling": false, "actions": [], "tags": []}, {"id": "sonar", "object": "model", "created": **********, "owned_by": "perplexity", "name": "Sonar", "openai": {"id": "sonar", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Sonar is lightweight, affordable, fast, and simple to use — now featuring citations and the ability to customize sources. It is designed for companies seeking to integrate lightweight question-and-answer features optimized for speed.", "modalities": {"input": ["text", "image"], "output": ["text"]}, "provider": "perplexity", "profile_image_url": "/static/perplexity.png"}}, "context_length": 127072, "reasoning_level": "fast", "auto_select_priority": 0, "premium_model": false, "supports_function_calling": false, "actions": [], "tags": []}, {"id": "sonar-reasoning", "object": "model", "created": **********, "owned_by": "perplexity", "name": "Sonar Reasoning", "openai": {"id": "sonar-reasoning", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Sonar Reasoning is a reasoning model provided by Perplexity based on [DeepSeek R1](/deepseek/deepseek-r1).\n\nIt allows developers to utilize long chain of thought with built-in web search. Sonar Reasoning is uncensored and hosted in US datacenters. ", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "perplexity", "profile_image_url": "/static/perplexity.png"}}, "context_length": 127000, "reasoning_level": "light", "auto_select_priority": 0, "premium_model": false, "supports_function_calling": false, "actions": [], "tags": []}, {"id": "sonar-reasoning-pro", "object": "model", "created": **********, "owned_by": "perplexity", "name": "Sonar Reasoning Pro", "openai": {"id": "sonar-reasoning-pro", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Note: Sonar Pro pricing includes Perplexity search pricing. See [details here](https://docs.perplexity.ai/guides/pricing#detailed-pricing-breakdown-for-sonar-reasoning-pro-and-sonar-pro)\n\nSonar Reasoning Pro is a premier reasoning model powered by DeepSeek R1 with Chain of Thought (CoT). Designed for advanced use cases, it supports in-depth, multi-step queries with a larger context window and can surface more citations per search, enabling more comprehensive and extensible responses.", "modalities": {"input": ["text", "image"], "output": ["text"]}, "provider": "perplexity", "profile_image_url": "/static/perplexity.png"}}, "context_length": 128000, "reasoning_level": "heavy", "auto_select_priority": 0, "premium_model": false, "supports_function_calling": false, "actions": [], "tags": []}, {"id": "spotlight", "object": "model", "created": **********, "owned_by": "arcee-ai", "name": "Spotlight", "openai": {"id": "spotlight", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Spotlight is a 7‑billion‑parameter vision‑language model derived from Qwen 2.5‑VL and fine‑tuned by Arcee AI for tight image‑text grounding tasks. It offers a 32 k‑token context window, enabling rich multimodal conversations that combine lengthy documents with one or more images. Training emphasized fast inference on consumer GPUs while retaining strong captioning, visual‐question‑answering, and diagram‑analysis accuracy. As a result, Spotlight slots neatly into agent workflows where screenshots, charts or UI mock‑ups need to be interpreted on the fly. Early benchmarks show it matching or out‑scoring larger VLMs such as LLaVA‑1.6 13 B on popular VQA and POPE alignment tests. ", "modalities": {"input": ["image", "text"], "output": ["text"]}, "provider": "arcee-ai", "profile_image_url": "/static/favicon.png"}}, "context_length": 131072, "reasoning_level": "light", "premium_model": false, "supports_function_calling": false, "actions": [], "tags": []}, {"id": "router", "object": "model", "created": **********, "owned_by": "switchpoint", "name": "Switchpoint Router", "openai": {"id": "router", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Switchpoint AI's router instantly analyzes your request and directs it to the optimal AI from an ever-evolving library. \n\nAs the world of LLMs advances, our router gets smarter, ensuring you always benefit from the industry's newest models without changing your workflow.\n\nThis model is configured for a simple, flat rate per response here on OpenRouter. It's powered by the full routing engine from [Switchpoint AI](https://www.switchpoint.dev).", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "switchpoint", "profile_image_url": "/static/favicon.png"}}, "context_length": 131072, "reasoning_level": "light", "premium_model": false, "supports_function_calling": false, "actions": [], "tags": []}, {"id": "ui-tars-1.5-7b", "object": "model", "created": **********, "owned_by": "bytedance", "name": "UI-TARS 7B ", "openai": {"id": "ui-tars-1.5-7b", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "UI-TARS-1.5 is a multimodal vision-language agent optimized for GUI-based environments, including desktop interfaces, web browsers, mobile systems, and games. Built by ByteDance, it builds upon the UI-TARS framework with reinforcement learning-based reasoning, enabling robust action planning and execution across virtual interfaces.\n\nThis model achieves state-of-the-art results on a range of interactive and grounding benchmarks, including OSworld, WebVoyager, AndroidWorld, and ScreenSpot. It also demonstrates perfect task completion across diverse Poki games and outperforms prior models in Minecraft agent tasks. UI-TARS-1.5 supports thought decomposition during inference and shows strong scaling across variants, with the 1.5 version notably exceeding the performance of earlier 72B and 7B checkpoints.", "modalities": {"input": ["image", "text"], "output": ["text"]}, "provider": "bytedance", "profile_image_url": "/static/favicon.png"}}, "context_length": 128000, "reasoning_level": "fast", "premium_model": false, "supports_function_calling": false, "actions": [], "tags": []}, {"id": "virtuoso-large", "object": "model", "created": **********, "owned_by": "arcee-ai", "name": "Virtuoso Large", "openai": {"id": "virtuoso-large", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Virtuoso‑Large is Arcee's top‑tier general‑purpose LLM at 72 B parameters, tuned to tackle cross‑domain reasoning, creative writing and enterprise QA. Unlike many 70 B peers, it retains the 128 k context inherited from Qwen 2.5, letting it ingest books, codebases or financial filings wholesale. Training blended DeepSeek R1 distillation, multi‑epoch supervised fine‑tuning and a final DPO/RLHF alignment stage, yielding strong performance on BIG‑Bench‑Hard, GSM‑8K and long‑context Needle‑In‑Haystack tests. Enterprises use Virtuoso‑Large as the \"fallback\" brain in Conductor pipelines when other SLMs flag low confidence. Despite its size, aggressive KV‑cache optimizations keep first‑token latency in the low‑second range on 8× H100 nodes, making it a practical production‑grade powerhouse.", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "arcee-ai", "profile_image_url": "/static/favicon.png"}}, "context_length": 131072, "reasoning_level": "fast", "premium_model": false, "supports_function_calling": true, "actions": [], "tags": []}, {"id": "wizardlm-2-8x22b", "object": "model", "created": **********, "owned_by": "microsoft", "name": "WizardLM-2 8x22B", "openai": {"id": "wizardlm-2-8x22b", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "WizardLM-2 8x22B is Microsoft AI's most advanced Wizard model. It demonstrates highly competitive performance compared to leading proprietary models, and it consistently outperforms all existing state-of-the-art opensource models.\n\nIt is an instruct finetune of [Mixtral 8x22B](/models/mistralai/mixtral-8x22b).\n\nTo read more about the model release, [click here](https://wizardlm.github.io/WizardLM2/).\n\n#moe", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "microsoft", "profile_image_url": "/static/microsoft.png"}}, "context_length": 65536, "reasoning_level": "fast", "premium_model": false, "supports_function_calling": false, "actions": [], "tags": []}, {"id": "allam-2-7b", "object": "model", "created": **********, "owned_by": "groq", "name": "allam-2-7b", "openai": {"id": "allam-2-7b", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"modalities": {"input": ["text"], "output": ["text"]}, "provider": "groq", "profile_image_url": "/static/favicon.png"}}, "context_length": 4096, "reasoning_level": "fast", "premium_model": false, "supports_function_calling": false, "actions": [], "tags": []}, {"id": "compound", "object": "model", "created": **********, "owned_by": "groq", "name": "compound-beta", "openai": {"id": "compound", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"modalities": {"input": ["text"], "output": ["text"]}, "provider": "groq", "profile_image_url": "/static/favicon.png"}}, "context_length": 131072, "reasoning_level": "light", "premium_model": false, "supports_function_calling": false, "actions": [], "tags": []}, {"id": "compound-beta-mini", "object": "model", "created": **********, "owned_by": "groq", "name": "compound-beta-mini", "openai": {"id": "compound-beta-mini", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"modalities": {"input": ["text"], "output": ["text"]}, "provider": "groq", "profile_image_url": "/static/favicon.png"}}, "context_length": 8192, "reasoning_level": "fast", "premium_model": false, "supports_function_calling": false, "actions": [], "tags": []}, {"id": "dall-e-2", "object": "model", "created": **********, "owned_by": "openai", "name": "dall-e-2", "openai": {"id": "dall-e-2", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"modalities": {"input": ["text"], "output": ["image"]}, "provider": "openai", "profile_image_url": "/static/openai.png"}}, "reasoning_level": "light", "premium_model": true, "supports_function_calling": false, "actions": [], "tags": []}, {"id": "dall-e-3", "object": "model", "created": **********, "owned_by": "openai", "name": "dall-e-3", "openai": {"id": "dall-e-3", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"modalities": {"input": ["text"], "output": ["image"]}, "provider": "openai", "profile_image_url": "/static/openai.png"}}, "reasoning_level": "light", "premium_model": true, "supports_function_calling": false, "actions": [], "tags": []}, {"id": "deepseek-r1-distill-llama-70b", "object": "model", "created": **********, "owned_by": "groq", "name": "deepseek-r1-distill-llama-70b", "openai": {"id": "deepseek-r1-distill-llama-70b", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"modalities": {"input": ["text"], "output": ["text"]}, "provider": "groq", "profile_image_url": "/static/favicon.png"}}, "context_length": 131072, "reasoning_level": "fast", "premium_model": false, "supports_function_calling": false, "actions": [], "tags": []}, {"id": "gemma-3-1b-it", "object": "model", "created": **********, "owned_by": "google", "name": "gemma-3-1b-it", "openai": {"id": "gemma-3-1b-it", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"modalities": {"input": ["text"], "output": ["text"]}, "provider": "google", "profile_image_url": "/static/google.png"}}, "reasoning_level": "fast", "premium_model": false, "supports_function_calling": false, "actions": [], "tags": []}, {"id": "gpt-5-chat-latest", "object": "model", "created": **********, "owned_by": "openai", "name": "gpt-5-chat-latest", "openai": {"id": "gpt-5-chat-latest", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"modalities": {"input": ["text"], "output": ["text"]}, "provider": "openai", "profile_image_url": "/static/openai.png"}}, "reasoning_level": "light", "premium_model": false, "supports_function_calling": false, "actions": [], "tags": []}, {"id": "gpt-image-1", "object": "model", "created": **********, "owned_by": "openai", "name": "gpt-image-1", "openai": {"id": "gpt-image-1", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"modalities": {"input": ["text"], "output": ["image"]}, "provider": "openai", "profile_image_url": "/static/openai.png"}}, "reasoning_level": "fast", "premium_model": true, "supports_function_calling": false, "actions": [], "tags": []}, {"id": "imagen-3.0-generate-002", "object": "model", "created": **********, "owned_by": "google", "name": "imagen-3.0-generate-002", "openai": {"id": "imagen-3.0-generate-002", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"modalities": {"input": ["text"], "output": ["image"]}, "provider": "google", "profile_image_url": "/static/google.png"}}, "reasoning_level": "light", "premium_model": true, "supports_function_calling": false, "actions": [], "tags": []}, {"id": "imagen-4.0-fast-generate-001", "object": "model", "created": **********, "owned_by": "gemini", "name": "imagen-4.0-fast-generate-001", "openai": {"id": "imagen-4.0-fast-generate-001", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"modalities": {"input": ["text"], "output": ["text"]}, "provider": "gemini", "profile_image_url": "/static/favicon.png"}}, "reasoning_level": "fast", "premium_model": false, "supports_function_calling": false, "actions": [], "tags": []}, {"id": "imagen-4.0-generate-001", "object": "model", "created": **********, "owned_by": "gemini", "name": "imagen-4.0-generate-001", "openai": {"id": "imagen-4.0-generate-001", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"modalities": {"input": ["text"], "output": ["text"]}, "provider": "gemini", "profile_image_url": "/static/favicon.png"}}, "reasoning_level": "light", "premium_model": false, "supports_function_calling": false, "actions": [], "tags": []}, {"id": "imagen-4.0-generate", "object": "model", "created": **********, "owned_by": "google", "name": "imagen-4.0-generate-preview-06-06", "openai": {"id": "imagen-4.0-generate", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"modalities": {"input": ["text"], "output": ["image"]}, "provider": "google", "profile_image_url": "/static/google.png"}}, "reasoning_level": "light", "premium_model": true, "supports_function_calling": false, "actions": [], "tags": []}, {"id": "imagen-4.0-ultra-generate-001", "object": "model", "created": **********, "owned_by": "gemini", "name": "imagen-4.0-ultra-generate-001", "openai": {"id": "imagen-4.0-ultra-generate-001", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"modalities": {"input": ["text"], "output": ["text"]}, "provider": "gemini", "profile_image_url": "/static/favicon.png"}}, "reasoning_level": "heavy", "premium_model": false, "supports_function_calling": false, "actions": [], "tags": []}, {"id": "imagen-4.0-ultra-generate", "object": "model", "created": **********, "owned_by": "google", "name": "imagen-4.0-ultra-generate-preview-06-06", "openai": {"id": "imagen-4.0-ultra-generate", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"modalities": {"input": ["text"], "output": ["image"]}, "provider": "google", "profile_image_url": "/static/google.png"}}, "reasoning_level": "light", "premium_model": true, "supports_function_calling": false, "actions": [], "tags": []}, {"id": "kimi-k2-instruct", "object": "model", "created": **********, "owned_by": "groq", "name": "kimi-k2-instruct", "openai": {"id": "kimi-k2-instruct", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"modalities": {"input": ["text"], "output": ["text"]}, "provider": "groq", "profile_image_url": "/static/favicon.png"}}, "context_length": 131072, "reasoning_level": "light", "premium_model": false, "supports_function_calling": false, "actions": [], "tags": []}, {"id": "llama-3.3-70b-versatile", "object": "model", "created": **********, "owned_by": "groq", "name": "llama-3.3-70b-versatile", "openai": {"id": "llama-3.3-70b-versatile", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"modalities": {"input": ["text"], "output": ["text"]}, "provider": "groq", "profile_image_url": "/static/favicon.png"}}, "context_length": 131072, "reasoning_level": "fast", "premium_model": false, "supports_function_calling": false, "actions": [], "tags": []}, {"id": "o1", "object": "model", "created": **********, "owned_by": "openai", "name": "o1", "openai": {"id": "o1", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "The latest and strongest model family from OpenAI, o1 is designed to spend more time thinking before responding. The o1 model series is trained with large-scale reinforcement learning to reason using chain of thought. \n\nThe o1 models are optimized for math, science, programming, and other STEM-related tasks. They consistently exhibit PhD-level accuracy on benchmarks in physics, chemistry, and biology. Learn more in the [launch announcement](https://openai.com/o1).\n", "modalities": {"input": ["text", "image"], "output": ["text"]}, "provider": "openai", "profile_image_url": "/static/openai.png"}}, "context_length": 200000, "reasoning_level": "heavy", "auto_select_priority": 0, "premium_model": true, "supports_function_calling": true, "actions": [], "tags": []}, {"id": "o1-mini", "object": "model", "created": **********, "owned_by": "openai", "name": "o1-mini", "openai": {"id": "o1-mini", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "The latest and strongest model family from OpenAI, o1 is designed to spend more time thinking before responding.\n\nThe o1 models are optimized for math, science, programming, and other STEM-related tasks. They consistently exhibit PhD-level accuracy on benchmarks in physics, chemistry, and biology. Learn more in the [launch announcement](https://openai.com/o1).\n\nNote: This model is currently experimental and not suitable for production use-cases, and may be heavily rate-limited.", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "openai", "profile_image_url": "/static/openai.png"}}, "context_length": 128000, "reasoning_level": "light", "premium_model": false, "supports_function_calling": false, "actions": [], "tags": []}, {"id": "o3", "object": "model", "created": **********, "owned_by": "openai", "name": "o3", "openai": {"id": "o3", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "o3 is a well-rounded and powerful model across domains. It sets a new standard for math, science, coding, and visual reasoning tasks. It also excels at technical writing and instruction-following. Use it to think through multi-step problems that involve analysis across text, code, and images. Note that BYOK is required for this model. Set up here: https://openrouter.ai/settings/integrations", "modalities": {"input": ["image", "text", "file"], "output": ["text"]}, "provider": "openai", "profile_image_url": "/static/openai.png"}}, "context_length": 200000, "reasoning_level": "heavy", "premium_model": true, "supports_function_calling": false, "actions": [], "tags": []}, {"id": "o3-mini", "object": "model", "created": **********, "owned_by": "openai", "name": "o3 Mini", "openai": {"id": "o3-mini", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "OpenAI o3-mini is a cost-efficient language model optimized for STEM reasoning tasks, particularly excelling in science, mathematics, and coding.\n\nThe model demonstrates significant improvements over its predecessor, with expert testers preferring its responses 56% of the time and noting a 39% reduction in major errors on complex questions. With medium reasoning effort settings, o3-mini matches the performance of the larger o1 model on challenging reasoning evaluations like AIME and GPQA, while maintaining lower latency and cost.", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "openai", "profile_image_url": "/static/openai.png"}}, "context_length": 200000, "reasoning_level": "light", "auto_select_priority": 0, "premium_model": false, "supports_function_calling": true, "actions": [], "tags": []}, {"id": "o3-mini-high", "object": "model", "created": **********, "owned_by": "openai", "name": "o3 Mini High", "openai": {"id": "o3-mini-high", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "OpenAI o3-mini-high is the same model as [o3-mini](/openai/o3-mini) with reasoning_effort set to high. \n\no3-mini is a cost-efficient language model optimized for STEM reasoning tasks, particularly excelling in science, mathematics, and coding. The model features three adjustable reasoning effort levels and supports key developer capabilities including function calling, structured outputs, and streaming, though it does not include vision processing capabilities.\n\nThe model demonstrates significant improvements over its predecessor, with expert testers preferring its responses 56% of the time and noting a 39% reduction in major errors on complex questions. With medium reasoning effort settings, o3-mini matches the performance of the larger o1 model on challenging reasoning evaluations like AIME and GPQA, while maintaining lower latency and cost.", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "openai", "profile_image_url": "/static/openai.png"}}, "context_length": 200000, "reasoning_level": "fast", "premium_model": false, "supports_function_calling": true, "actions": [], "tags": []}, {"id": "o3-deep-research", "object": "model", "created": **********, "owned_by": "openai", "name": "o3-deep-research", "openai": {"id": "o3-deep-research", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"modalities": {"input": ["text"], "output": ["text"]}, "provider": "openai", "profile_image_url": "/static/openai.png"}}, "reasoning_level": "heavy", "premium_model": false, "supports_function_calling": false, "actions": [], "tags": []}, {"id": "o4-mini", "object": "model", "created": **********, "owned_by": "openai", "name": "o4 Mini", "openai": {"id": "o4-mini", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "OpenAI o4-mini is a compact reasoning model in the o-series, optimized for fast, cost-efficient performance while retaining strong multimodal and agentic capabilities. It supports tool use and demonstrates competitive reasoning and coding performance across benchmarks like AIME (99.5% with Python) and SWE-bench, outperforming its predecessor o3-mini and even approaching o3 in some domains.\n\nDespite its smaller size, o4-mini exhibits high accuracy in STEM tasks, visual problem solving (e.g., MathVista, MMMU), and code editing. It is especially well-suited for high-throughput scenarios where latency or cost is critical. Thanks to its efficient architecture and refined reinforcement learning training, o4-mini can chain tools, generate structured outputs, and solve multi-step tasks with minimal delay—often in under a minute.", "modalities": {"input": ["image", "text"], "output": ["text"]}, "provider": "openai", "profile_image_url": "/static/openai.png"}}, "context_length": 200000, "reasoning_level": "light", "premium_model": false, "supports_function_calling": true, "actions": [], "tags": []}, {"id": "o4-mini-high", "object": "model", "created": **********, "owned_by": "openai", "name": "o4 Mini High", "openai": {"id": "o4-mini-high", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "OpenAI o4-mini-high is the same model as [o4-mini](/openai/o4-mini) with reasoning_effort set to high. \n\nOpenAI o4-mini is a compact reasoning model in the o-series, optimized for fast, cost-efficient performance while retaining strong multimodal and agentic capabilities. It supports tool use and demonstrates competitive reasoning and coding performance across benchmarks like AIME (99.5% with Python) and SWE-bench, outperforming its predecessor o3-mini and even approaching o3 in some domains.\n\nDespite its smaller size, o4-mini exhibits high accuracy in STEM tasks, visual problem solving (e.g., MathVista, MMMU), and code editing. It is especially well-suited for high-throughput scenarios where latency or cost is critical. Thanks to its efficient architecture and refined reinforcement learning training, o4-mini can chain tools, generate structured outputs, and solve multi-step tasks with minimal delay—often in under a minute.", "modalities": {"input": ["image", "text", "file"], "output": ["text"]}, "provider": "openai", "profile_image_url": "/static/openai.png"}}, "context_length": 200000, "reasoning_level": "heavy", "premium_model": false, "supports_function_calling": true, "actions": [], "tags": []}, {"id": "o4-mini-deep-research", "object": "model", "created": **********, "owned_by": "openai", "name": "o4-mini-deep-research", "openai": {"id": "o4-mini-deep-research", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"modalities": {"input": ["text"], "output": ["text"]}, "provider": "openai", "profile_image_url": "/static/openai.png"}}, "reasoning_level": "heavy", "premium_model": false, "supports_function_calling": false, "actions": [], "tags": []}]}
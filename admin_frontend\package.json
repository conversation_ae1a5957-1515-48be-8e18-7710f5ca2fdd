{"name": "chatbetter2admin-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@nextui-org/react": "^2.4.8", "autoprefixer": "^10.4.21", "axios": "^1.5.0", "dayjs": "^1.11.10", "jwt-decode": "^3.1.2", "next-themes": "^0.2.1", "postcss": "^8.4.31", "react": "^18.2.0", "react-dom": "^18.2.0", "react-icons": "^5.5.0", "react-router-dom": "^6.15.0", "tailwindcss": "^3.4.4"}, "devDependencies": {"@types/react": "^18.2.14", "@types/react-dom": "^18.2.6", "@vitejs/plugin-react": "^4.0.0", "typescript": "^5.2.2", "vite": "^4.4.3"}}